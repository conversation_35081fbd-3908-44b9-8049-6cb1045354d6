# 考场编排通用工具使用说明 - 重构版 v2.0

## 功能概述

本工具是考场编排功能的重构版本，实现了WPS中的考场编排功能，支持桌号管理，包括单人座位和双人座位两种模式。

## 重构版主要变化

### 数据结构变化
- **原版**：学校、校点、年级、班级、准考号、姓名、考场号、**座位号**
- **重构版**：学校、校点、年级、班级、准考号、姓名、考场号、**桌号**

### 桌号管理逻辑
- **单人座位**：每桌坐1人，桌号不重复，一个桌号对应一个学生
- **双人座位**：每桌坐2人，桌号可重复，一个桌号对应两个学生

### 函数重命名（解决二义性）
为避免与原版函数冲突，重构版中的关键函数已重命名：
- `CreateCrossGradePairing` → `CreateCrossGradeTablePairing`
- `CreateCrossClassArrangementForSingleGrade` → `CreateCrossClassArrangementForSingleGradeV2`

## 数据结构要求

### 工作表名称
- 默认工作表名称：`考场编排`
- 可在代码中修改 `g_WorksheetName` 变量来更改工作表名称

### 数据列结构
工作表应包含以下列（按顺序）：

| 列号 | 列名 | 说明 |
|------|------|------|
| A | 学校 | 学校名称 |
| B | 校点 | 校点名称 |
| C | 年级 | 年级信息（如：三年级、四年级） |
| D | 班级 | 班级信息 |
| E | 准考号 | 学生准考号 |
| F | 姓名 | 学生姓名 |
| G | 考场号 | 程序自动填写 |
| H | 桌号 | 程序自动填写 |

## 使用步骤

### 1. 准备数据
- 确保工作表名称为"考场编排"
- 按照上述列结构整理学生数据
- 第一行应为列标题

### 2. 运行程序
- 在VBA编辑器中运行 `Main()` 子程序
- 或者在WPS中按 Alt+F8 选择 Main 运行

### 3. 设置参数

#### 考场容量设置
- 输入每个考场的桌子数量（如：30）
- 必须为正整数

#### 座位模式选择
- **单人座位**：每桌坐1人，桌号不重复
- **双人座位**：每桌坐2人，桌号可重复（需要设置年级组合）

#### 年级组合设置（仅双人模式）
- 最多可设置4个年级组合
- 每个组合可包含多个年级（用逗号分隔）
- 示例：
  - 组合1：三年级,四年级
  - 组合2：五年级,六年级
- 同一组合内的不同年级学生可以坐在同一桌

## 编排规则

### 基本规则
1. **独立编排**：各学校各校点独立建考场序号
2. **连续编号**：校点内各考场连续编号
3. **重置机制**：学校、校点不同时，重置考场号和桌号为1
4. **唯一标识**：学校+校点+年级+班级组合作为唯一标识

### 单人座位模式
- 按学校+校点分组
- 每个学生占用一个桌子
- 桌号从1开始连续编号，不重复
- **交叉编排**：同校、同校点、不同年级班级学生交叉编排

### 双人座位模式
- 按学校+校点+年级组合分组
- **跨年级配对**：确保同桌的两个学生来自不同年级
- 每桌两个学生共用相同的桌号
- **桌号重复**：同一考场内桌号可重复，每桌坐2人

### 交叉编排详细说明

#### 交叉编排原理
交叉编排确保同一学校、同一校点的不同年级班级学生能够交替排列，避免同年级班级学生集中在一起。

#### 编排算法
1. **多年级班级情况**：
   - 按轮次从各年级班级依次取学生
   - 例如：三1班第1个 → 三2班第1个 → 四1班第1个 → 三1班第2个...
   - 结果：不同年级班级学生交替出现

2. **单年级班级情况**：
   - 如果某校点只有一个年级班级，则按原顺序排列
   - 不进行交叉编排

#### 双人座位跨年级配对
在双人座位模式下：
1. **跨年级配对**：同桌的两个学生必须来自不同年级
2. **配对规则**：三年级学生与四年级学生配对，五年级学生与六年级学生配对
3. **桌号管理**：每桌有相同桌号，两个学生共用
4. **单年级处理**：如果年级组合中只有一个年级，则按班级交叉编排后配对

#### 跨年级配对示例
假设三、四年级组合，每年级2人：
- **配对前**：三年级(张三,李四), 四年级(王五,赵六)
- **配对后**：1号桌(张三,王五), 2号桌(李四,赵六)
- **结果**：每桌都是三年级+四年级的组合

## 性能优化

### 数组和字典优化
- 使用数组读取数据，提高读取速度
- 使用字典进行分组，提高查找效率
- 关闭屏幕更新和自动计算，提升运行速度

### 内存管理
- 程序运行完成后自动恢复Excel设置
- 使用错误处理机制确保程序稳定性

## 注意事项

### 数据要求
1. 学校、校点、年级、班级列不能为空
2. 准考号和姓名建议填写完整
3. 数据应从第2行开始（第1行为标题）

### 年级组合设置
1. 双人模式下至少需要设置一个年级组合
2. 年级名称应与数据中的年级列完全匹配
3. 可以使用中文数字（如：三年级）或阿拉伯数字（如：3年级）

### 错误处理
- 如果工作表不存在，程序会提示错误
- 如果数据为空，程序会提示并退出
- 运行过程中如有错误，会显示错误信息

## 输出结果

程序运行完成后：
- G列（考场号）：显示学生所在考场编号
- H列（桌号）：显示学生的桌号
- 考场号按学校+校点独立编号
- 桌号在每个考场内从1开始编号

### 单人座位结果示例
```
考场1：桌号 1, 2, 3, 4, 5, 6...（每桌1人，桌号不重复）
考场2：桌号 1, 2, 3, 4, 5, 6...（每桌1人，桌号不重复）
```

### 双人座位结果示例
```
考场1：桌号 1, 1, 2, 2, 3, 3...（每桌2人，桌号重复）
考场2：桌号 1, 1, 2, 2, 3, 3...（每桌2人，桌号重复）
```

## 使用示例

### 示例1：单人座位编排
```vba
' 直接调用主程序
Sub RunSingleSeating()
    Call Main()
    ' 在对话框中选择：
    ' 1. 考场容量：30（桌子数量）
    ' 2. 座位模式：选择"否"（单人座位）
    ' 结果：每桌1人，桌号1,2,3,4...不重复
End Sub
```

### 示例2：双人座位编排
```vba
' 直接调用主程序
Sub RunDoubleSeating()
    Call Main()
    ' 在对话框中选择：
    ' 1. 考场容量：30（桌子数量）
    ' 2. 座位模式：选择"是"（双人座位）
    ' 3. 年级组合设置：
    '    - 组合1：三年级,四年级
    '    - 组合2：五年级,六年级
    ' 结果：每桌2人，桌号1,1,2,2,3,3...重复
End Sub
```

### 示例3：测试功能
```vba
' 运行完整测试流程
Sub RunCompleteTestV2()
    ' 1. 创建测试数据
    Call CreateTestDataV2()

    ' 2. 测试单人座位编排
    Call TestSingleSeatingV2()

    ' 3. 清空结果
    Call ClearArrangementV2()

    ' 4. 测试双人座位编排
    Call TestDoubleSeatingV2()
End Sub
```

## 版本更新记录

### v2.0 (2024) - 重构版
- **重大重构**：将"座位号"改为"桌号"概念
- **桌号管理**：单人座位桌号不重复，双人座位桌号可重复
- **优化算法**：简化编排逻辑，提高性能
- **增强测试**：添加桌号编排分析功能
- **完善文档**：详细说明桌号管理原理

### v1.2 (2024)
- 双人座位模式改为跨年级配对
- 新增A座、B座标识系统
- 配对规则：确保同桌学生来自不同年级

### v1.1 (2024)
- 新增功能：同校、同年级、不同班交叉编排
- 优化算法：改进配对逻辑

### v1.0 (2024)
- 初始版本
- 支持单人和双人座位编排

## 技术特点

1. **模块化设计**：功能分离，便于维护
2. **错误处理**：完善的错误处理机制
3. **性能优化**：使用数组和字典提高效率
4. **用户友好**：简单的对话框界面
5. **灵活配置**：支持多种座位模式和年级组合
6. **桌号管理**：智能处理单人/双人座位的桌号分配

## 联系支持

如有问题或需要定制功能，请联系开发人员。

## 附录：完整代码文件列表

1. **考场编排工具重构版.vbs** - 主程序文件
2. **测试示例重构版.vbs** - 测试和示例代码
3. **考场编排工具使用说明重构版.md** - 本说明文档

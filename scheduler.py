"""
Task scheduling functionality.
"""
import os
import time
import logging
import threading
import subprocess
import psutil
from datetime import datetime, timedelta

import utils
from config import Config

logger = logging.getLogger("定时关机")

class ScheduledTask:
    """A scheduled task."""

    def __init__(self, task_id, name, task_type, schedule_type, schedule_time, parameters=None):
        """Initialize a scheduled task.

        Args:
            task_id: A unique identifier for the task.
            name: The name of the task.
            task_type: The type of task (shutdown, restart, lock, remind, open_file, execute, close).
            schedule_type: The type of schedule (once, daily, weekly, monthly, yearly).
            schedule_time: The time to schedule the task.
            parameters: Additional parameters for the task.
        """
        self.task_id = task_id
        self.name = name
        self.task_type = task_type
        self.schedule_type = schedule_type
        self.schedule_time = schedule_time
        self.parameters = parameters or {}
        self.last_run = None
        self.next_run = None
        self.enabled = True

        self._calculate_next_run()

    def _calculate_next_run(self):
        """Calculate the next run time for the task."""
        now = datetime.now()

        if self.schedule_type == "once":
            # Schedule time is a datetime
            self.next_run = self.schedule_time
            if self.next_run < now:
                self.next_run = None  # Task is in the past and won't run again

        elif self.schedule_type == "daily":
            # Schedule time is a time
            next_run = datetime.combine(now.date(), self.schedule_time)
            if next_run < now:
                next_run += timedelta(days=1)
            self.next_run = next_run

        elif self.schedule_type == "weekly":
            # Schedule time is a tuple of (weekday, time)
            weekday, time_of_day = self.schedule_time
            days_ahead = weekday - now.weekday()
            if days_ahead < 0 or (days_ahead == 0 and now.time() > time_of_day):
                days_ahead += 7
            next_date = now.date() + timedelta(days=days_ahead)
            self.next_run = datetime.combine(next_date, time_of_day)

        elif self.schedule_type == "monthly":
            # Schedule time is a tuple of (day, time)
            day, time_of_day = self.schedule_time

            # Try to set for current month
            try:
                next_run = datetime(now.year, now.month, day, time_of_day.hour, time_of_day.minute)
                if next_run < now:
                    # Move to next month
                    if now.month == 12:
                        next_run = datetime(now.year + 1, 1, day, time_of_day.hour, time_of_day.minute)
                    else:
                        next_run = datetime(now.year, now.month + 1, day, time_of_day.hour, time_of_day.minute)
            except ValueError:
                # Day is invalid for current month (e.g., February 30)
                # Move to next month
                if now.month == 12:
                    next_month = 1
                    next_year = now.year + 1
                else:
                    next_month = now.month + 1
                    next_year = now.year

                # Find the last day of the next month
                if day > 28:
                    if next_month == 2:
                        if (next_year % 4 == 0 and next_year % 100 != 0) or (next_year % 400 == 0):
                            day = min(day, 29)  # Leap year
                        else:
                            day = 28
                    elif next_month in [4, 6, 9, 11]:
                        day = min(day, 30)

                next_run = datetime(next_year, next_month, day, time_of_day.hour, time_of_day.minute)

            self.next_run = next_run

        elif self.schedule_type == "yearly":
            # Schedule time is a tuple of (month, day, time)
            month, day, time_of_day = self.schedule_time

            # Try to set for current year
            try:
                next_run = datetime(now.year, month, day, time_of_day.hour, time_of_day.minute)
                if next_run < now:
                    # Move to next year
                    next_run = datetime(now.year + 1, month, day, time_of_day.hour, time_of_day.minute)
            except ValueError:
                # Day is invalid for the month (e.g., February 29 in a non-leap year)
                # Move to next year
                next_year = now.year + 1

                # Find a valid day
                if month == 2 and day == 29:
                    if (next_year % 4 == 0 and next_year % 100 != 0) or (next_year % 400 == 0):
                        day = 29  # Leap year
                    else:
                        day = 28

                next_run = datetime(next_year, month, day, time_of_day.hour, time_of_day.minute)

            self.next_run = next_run

    def to_dict(self):
        """Convert the task to a dictionary for serialization."""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "task_type": self.task_type,
            "schedule_type": self.schedule_type,
            "schedule_time": self._serialize_schedule_time(),
            "parameters": self.parameters,
            "last_run": self.last_run.isoformat() if self.last_run else None,
            "enabled": self.enabled
        }

    @classmethod
    def from_dict(cls, data):
        """Create a task from a dictionary."""
        task = cls(
            task_id=data["task_id"],
            name=data["name"],
            task_type=data["task_type"],
            schedule_type=data["schedule_type"],
            schedule_time=cls._deserialize_schedule_time(data["schedule_type"], data["schedule_time"]),
            parameters=data.get("parameters", {})
        )

        if data.get("last_run"):
            task.last_run = datetime.fromisoformat(data["last_run"])

        task.enabled = data.get("enabled", True)
        task._calculate_next_run()

        return task

    def _serialize_schedule_time(self):
        """Serialize the schedule time for storage."""
        if self.schedule_type == "once":
            return self.schedule_time.isoformat()
        elif self.schedule_type == "daily":
            return self.schedule_time.isoformat()
        elif self.schedule_type == "weekly":
            weekday, time_of_day = self.schedule_time
            return [weekday, time_of_day.isoformat()]
        elif self.schedule_type == "monthly":
            day, time_of_day = self.schedule_time
            return [day, time_of_day.isoformat()]
        elif self.schedule_type == "yearly":
            month, day, time_of_day = self.schedule_time
            return [month, day, time_of_day.isoformat()]

    @staticmethod
    def _deserialize_schedule_time(schedule_type, schedule_time):
        """Deserialize the schedule time from storage."""
        if schedule_type == "once":
            return datetime.fromisoformat(schedule_time)
        elif schedule_type == "daily":
            return datetime.fromisoformat(schedule_time).time()
        elif schedule_type == "weekly":
            weekday, time_str = schedule_time
            return (weekday, datetime.fromisoformat(time_str).time())
        elif schedule_type == "monthly":
            day, time_str = schedule_time
            return (day, datetime.fromisoformat(time_str).time())
        elif schedule_type == "yearly":
            month, day, time_str = schedule_time
            return (month, day, datetime.fromisoformat(time_str).time())

    def execute(self):
        """Execute the task."""
        logger.info(f"Executing scheduled task: {self.name} ({self.task_id})")

        self.last_run = datetime.now()

        if self.schedule_type != "once":
            self._calculate_next_run()
        else:
            self.next_run = None
            self.enabled = False

        task_handlers = {
            "shutdown": self._shutdown_computer,
            "restart": self._restart_computer,
            "lock": self._lock_computer,
            "remind": self._show_reminder,
            "open_file": self._open_file,
            "execute": self._execute_program,
            "open_url": self._open_url,
            "close": self._close_program,
            "disconnect_network": self._disconnect_network,
        }
        handler = task_handlers.get(self.task_type)
        if handler:
            handler()
        else:
            logger.error(f"Unknown task type: {self.task_type}")

    def _shutdown_computer(self):
        """Shutdown the computer."""
        logger.info("Shutting down computer")
        os.system("shutdown /s /t 60 /c \"定时关机: 系统将在60秒后关闭\"")

    def _restart_computer(self):
        """Restart the computer."""
        logger.info("Restarting computer")
        os.system("shutdown /r /t 60 /c \"定时关机: 系统将在60秒后重启\"")

    def _lock_computer(self):
        """Lock the computer."""
        logger.info("Locking computer")
        os.system("rundll32.exe user32.dll,LockWorkStation")

    def _show_reminder(self):
        """Show a reminder message."""
        message = self.parameters.get("message", "时间到了！")
        logger.info(f"Showing reminder: {message}")

        # Use PowerShell to display a notification
        ps_command = f'powershell -Command "& {{Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show(\'{message}\', \'定时关机\', [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)}}"'
        subprocess.Popen(ps_command, shell=True)

    def _open_file(self):
        """Open a file."""
        file_path = self.parameters.get("file_path", "")
        logger.info(f"Opening file: {file_path}")

        try:
            os.startfile(file_path)
        except Exception as e:
            logger.error(f"Error opening file: {e}")

    def _execute_program(self):
        """Execute a program."""
        program_path = self.parameters.get("program_path", "")
        logger.info(f"Executing program: {program_path}")

        try:
            subprocess.Popen(program_path, shell=True)
        except Exception as e:
            logger.error(f"Error executing program: {e}")

    def _open_url(self):
        """Open a URL."""
        url = self.parameters.get("url", "")
        logger.info(f"Opening URL: {url}")

        try:
            os.system(f'start {url}')
        except Exception as e:
            logger.error(f"Error opening URL: {e}")

    def _close_program(self):
        """Close a program."""
        program_name = self.parameters.get("program_name", "")
        logger.info(f"Closing program: {program_name}")

        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'].lower() == program_name.lower():
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.debug(f"Error terminating process: {e}")

    def _disconnect_network(self):
        """Disconnect from the network."""
        logger.info("Disconnecting from network")
        os.system('netsh interface set interface "以太网" admin=disable')
        os.system('netsh interface set interface "WLAN" admin=disable')


class TaskScheduler:
    """Schedule and manage tasks."""

    def __init__(self, config):
        """Initialize the task scheduler.

        Args:
            config: The application configuration.
        """
        self.config = config
        self.tasks = []
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        self._load_tasks()

    def start(self):
        """Start the task scheduler."""
        if self.is_running:
            return False

        self.stop_event.clear()
        self.scheduler_thread = threading.Thread(target=self._scheduler_thread, daemon=True)
        self.scheduler_thread.start()
        self.is_running = True

        logger.info("Started task scheduler")
        return True

    def stop(self):
        """Stop the task scheduler."""
        if not self.is_running:
            return False

        self.stop_event.set()
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=1.0)
        self.is_running = False

        logger.info("Stopped task scheduler")
        return True

    def add_task(self, task):
        """Add a task to the scheduler."""
        self.tasks.append(task)
        self._save_tasks()
        logger.info(f"Added task: {task.name} ({task.task_id})")
        return True

    def remove_task(self, task_id):
        """Remove a task from the scheduler."""
        for i, task in enumerate(self.tasks):
            if task.task_id == task_id:
                del self.tasks[i]
                self._save_tasks()
                logger.info(f"Removed task: {task.name} ({task.task_id})")
                return True

        logger.warning(f"Task not found: {task_id}")
        return False

    def get_task(self, task_id):
        """Get a task by ID."""
        for task in self.tasks:
            if task.task_id == task_id:
                return task

        return None

    def update_task(self, task_id, **kwargs):
        """Update a task."""
        task = self.get_task(task_id)
        if task is None:
            logger.warning(f"Task not found: {task_id}")
            return False

        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)

        if "schedule_type" in kwargs or "schedule_time" in kwargs:
            task._calculate_next_run()

        self._save_tasks()
        logger.info(f"Updated task: {task.name} ({task.task_id})")
        return True

    def _scheduler_thread(self):
        """Thread function to check and execute scheduled tasks."""
        while not self.stop_event.is_set():
            try:
                now = datetime.now()

                for task in self.tasks:
                    if task.enabled and task.next_run is not None and task.next_run <= now:
                        task.execute()
                        self._save_tasks()

                # Sleep until the next minute
                next_minute = (now + timedelta(minutes=1)).replace(second=0, microsecond=0)
                sleep_seconds = (next_minute - now).total_seconds()

                # Sleep in small increments to allow for stopping
                for _ in range(int(sleep_seconds)):
                    if self.stop_event.wait(timeout=1.0):  # 使用 wait 方法代替 sleep
                        break
            except Exception as e:
                logger.error(f"Error in scheduler thread: {e}")
                time.sleep(60.0)  # Wait a bit longer on error

    def _load_tasks(self):
        """Load tasks from configuration."""
        tasks_data = self.config.get("scheduled_tasks", [])

        self.tasks = []
        for task_data in tasks_data:
            try:
                task = ScheduledTask.from_dict(task_data)
                self.tasks.append(task)
            except Exception as e:
                logger.error(f"Error loading task: {e}")

    def _save_tasks(self):
        """Save tasks to configuration."""
        tasks_data = [task.to_dict() for task in self.tasks]
        self.config.set("scheduled_tasks", tasks_data)

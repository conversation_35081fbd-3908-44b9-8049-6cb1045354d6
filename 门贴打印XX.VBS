Sub 门贴打印()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim examSite As Variant ' 声明为 Variant 类型
    Dim examRoom As Integer
    Dim uniqueSites As Object
    Dim uniqueRooms As Object
    Dim i As Long
    Dim j As Long
    Dim k As Long
    Dim col As Long
    Dim newSheet As Worksheet ' 用于存储所有门贴的工作表
    Dim studentCount As Long

    ' 新增代码：删除所有以"考场"结尾的工作表
    Application.DisplayAlerts = False
    Dim sht As Worksheet
    For Each sht In ThisWorkbook.Sheets
        If Right(sht.Name, 2) = "考场" And sht.Name <> "学生信息汇总表" Then
            sht.Delete
        End If
    Next sht
    Application.DisplayAlerts = True
    
    ' 设置当前工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' 创建字典用于存储唯一的校点和考场
    Set uniqueSites = CreateObject("Scripting.Dictionary")
    Set uniqueRooms = CreateObject("Scripting.Dictionary")
    
    ' 按校点、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("F2:F" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:G" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 遍历数据，收集唯一的校点和考场
    For i = 2 To lastRow
        examSite = Trim(ws.Cells(i, 1).Value) ' 去除首尾空格
        If examSite <> "" Then ' 检查是否为空
            examRoom = ws.Cells(i, 6).Value
            If Not uniqueSites.Exists(examSite) Then
                uniqueSites.Add examSite, 1
            End If
            If Not uniqueRooms.Exists(examSite & "-" & examRoom) Then
                uniqueRooms.Add examSite & "-" & examRoom, 1
            End If
        End If
    Next i
        ' 检查是否存在名为“所有门贴”的工作表，如果存在则删除
    Dim tempWs As Worksheet
    For Each tempWs In ThisWorkbook.Sheets
        If tempWs.Name = "所有门贴" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            tempWs.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next tempWs
    
    ' 创建一个工作表用于存储所有门贴
    Set newSheet = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    newSheet.Name = "所有门贴"
    
    
    
    k = 1 ' 初始化 k 的值

    ' 按校点和考场顺序打印
    For Each examSite In uniqueSites.Keys
        Debug.Print "当前校点: " & examSite
        For Each examRoomKey In uniqueRooms.Keys
            Debug.Print "当前考场: " & examRoomKey
            If Left(examRoomKey, Len(examSite)) = examSite Then
                examRoom = Val(Mid(examRoomKey, InStr(examRoomKey, "-") + 1))
                
                ' 设置表头
                If k >= 1 And k <= newSheet.Rows.Count Then
                    newSheet.Cells(k, 1).Value = examSite & " 第" & CStr(examRoom) & "考场"
                    With newSheet.Cells(k, 1)
                        .Font.Name = "黑体"
                        .Font.Size = 36
                        .Font.Bold = True
                        .HorizontalAlignment = xlCenter
                        .VerticalAlignment = xlCenter
                    End With
                    newSheet.Rows(k).RowHeight = 43
                    ' 合并单元格范围，从第1列到第17列
                    newSheet.Range(newSheet.Cells(k, 1), newSheet.Cells(k, 17)).Merge
                Else
                    MsgBox "k 的值超出范围: " & k
                    Exit Sub
                End If
      k = k + 1 '''''
                ' 设置列标题，增加到三组
                col = 1 ' 从第1列开始，增加左侧边距
                For j = 1 To 3
                    If k + 2 <= newSheet.Rows.Count Then
                        newSheet.Cells(k + 2, col).Value = "座位"
                        newSheet.Cells(k + 2, col + 1).Value = "年级"
                        newSheet.Cells(k + 2, col + 2).Value = "班级"
                        newSheet.Cells(k + 2, col + 3).Value = "准考号"
                        newSheet.Cells(k + 2, col + 4).Value = "姓名"
                        For m = col To col + 6 ' 列索引增加 6
                            With newSheet.Cells(k + 2, m)
                                .Font.Name = "宋体"
                                .Font.Size = 14
                                .HorizontalAlignment = xlCenter
                                .VerticalAlignment = xlCenter
                            End With
                        Next m
                        If j < 3 Then ' 前两组添加分隔线
                            With newSheet.Range(newSheet.Cells(k + 2, col + 5), newSheet.Cells(newSheet.Rows.Count, col + 5)).Borders(xlEdgeRight)
                                .LineStyle = xlDot
                                .Weight = xlThin
                            End With
                        End If
                        col = col + 6 ' 列索引增加 6，预留分隔线位置
                    End If
                Next j
                
                ' 在第二行添加虚线底边
                If k + 1 <= newSheet.Rows.Count Then
                    With newSheet.Range(newSheet.Cells(k + 1, 1), newSheet.Cells(k + 1, 17)).Borders(xlEdgeBottom)
                        .LineStyle = xlDot
                        .Weight = xlThin
                    End With
                End If
                
                ' 填充本考场学生信息，增加到三组
                k = k + 2
                col = 1 ' 从第1列开始
                studentCount = 0
                For i = 2 To lastRow

                    Debug.Print "i: " & i, "examSite: " & examSite, "examRoom: " & examRoom
                    If ws.Cells(i, 1).Value = examSite And ws.Cells(i, 6).Value = examRoom Then
                        If k <= newSheet.Rows.Count And col <= newSheet.Columns.Count Then
                            newSheet.Cells(k, col).Value = ws.Cells(i, 7).Value
                            newSheet.Cells(k, col + 1).Value = ws.Cells(i, 2).Value
                            newSheet.Cells(k, col + 2).Value = ws.Cells(i, 3).Value
                            newSheet.Cells(k, col + 3).Value = ws.Cells(i, 4).Value
                            newSheet.Cells(k, col + 4).Value = ws.Cells(i, 5).Value
                            For m = col To col + 4
                                With newSheet.Cells(k, m)
                                    .Font.Name = "宋体"
                                    .Font.Size = 14
                                    .HorizontalAlignment = xlCenter
                                    .VerticalAlignment = xlCenter
                                End With
                            Next m
                            studentCount = studentCount + 1
                            If studentCount Mod 3 = 0 Then
                                k = k + 1
                                col = 1 ' 重置列索引为第1列
                            Else
                                col = col + 6 ' 列索引增加 6，预留分隔线位置
                            End If
                        End If
                    End If
                Next i
                
                ' 为每行数据添加分隔线
                ' 从第三行开始到本考场最后一行学生数据添加虚线底边和右边框
                If studentCount > 0 Then
                    For l = k To k + studentCount - 1
                        If l <= newSheet.Rows.Count Then
                            With newSheet.Range(newSheet.Cells(l, 1), newSheet.Cells(l, 17))
                                ' 添加底边虚线
                                .Borders(xlEdgeBottom).LineStyle = xlDot
                                .Borders(xlEdgeBottom).Weight = xlThin
                                ' 添加右边框虚线
                                .Borders(xlEdgeRight).LineStyle = xlDot
                                .Borders(xlEdgeRight).Weight = xlThin
                            End With
                        End If
                    Next l
                End If
                

                
                ' 在每个门贴之后插入分页符
                If k + 1 <= newSheet.Rows.Count Then
                    newSheet.HPageBreaks.Add Before:=newSheet.Rows(k + 1)
                End If
                k = k + 1 ' 更新 k 的值以准备下一个门贴
            End If
        Next examRoomKey
    Next examSite
    
        ' 设置页面布局为A4横向
    With newSheet.PageSetup
        .Orientation = xlLandscape
        .PaperSize = xlPaperA4
        .LeftMargin = Application.InchesToPoints(0.5) ' 设置左侧边距为0.5英寸
        .RightMargin = Application.InchesToPoints(0.5) ' 设置右侧边距为0.5英寸
        .TopMargin = Application.CentimetersToPoints(1)
        .BottomMargin = Application.CentimetersToPoints(1)
        ' 新增：设置页面内容水平居中打印
        .CenterHorizontally = True
    End With
    
    ' 缩小座位、年级、班级所在单元格列宽
    Dim colChars As Variant
    ' ABCFGHKLM列宽设置为5字符
    colChars = Array("A", "B", "C", "G", "H", "I", "M", "N", "O")
    Dim colVariant As Variant ' 新声明一个 Variant 类型的变量
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 5
    Next colVariant
    
    ' DJP列宽设置为11字符
    colChars = Array("D", "J", "P")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 11
    Next colVariant
    
    ' EKQ列宽设置为10字符
    colChars = Array("E", "K", "Q")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 14
    Next colVariant

    colChars = Array("F", "L")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 2
    Next colVariant
    
    ' 预览打印
    ' 新增性能优化设置
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    ' 优化数据读取：将数据一次性读入数组
    Dim dataArray As Variant
    dataArray = ws.Range("A2:G" & lastRow).Value
    
    ' 优化排序逻辑：改用数组排序替代工作表排序（此部分需要根据具体需求实现）...

    ' 优化字典填充：使用数组数据替代单元格访问
    For i = 1 To UBound(dataArray, 1)
        examSite = Trim(dataArray(i, 1))  ' A列数据
        If examSite <> "" Then
            examRoom = dataArray(i, 6)    ' F列数据
            uniqueSites(examSite) = 1
            uniqueRooms(examSite & "-" & examRoom) = 1
        End If
    Next i

    ' 恢复性能设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    newSheet.PrintPreview
End Sub

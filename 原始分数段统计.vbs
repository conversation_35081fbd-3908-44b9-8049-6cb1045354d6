' 辅助函数：处理学生统计和分数段计算
Private Function ProcessStatistics(dict As Object, key As String, studentKey As String, score As Variant) As Variant
    Dim statArray As Variant
    Dim studentDict As Object

    ' 如果字典中不存在该键，则初始化
    If Not dict.Exists(key) Then
        dict(key) = Array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        Set studentDict = CreateObject("Scripting.Dictionary")
        studentDict(studentKey) = 1
        Set dict(key & "_students") = studentDict
    Else
        Set studentDict = dict(key & "_students")
        If Not studentDict.Exists(studentKey) Then
            studentDict(studentKey) = 1
        End If
    End If

    ' 获取统计数组
    statArray = dict(key)
    statArray(0) = studentDict.Count ' 实有人数

    ' 处理分数
    If score <> "" Then
        statArray(1) = statArray(1) + 1 ' 参考人数
        statArray(19) = statArray(19) + CDbl(score) ' 总分，确保转换为 Double 类型

        ' 分数段统计
        Select Case CDbl(score) ' 确保转换为 Double 类型
            Case Is < 470
                statArray(2) = statArray(2) + 1
            Case 470 To 509.999999
                statArray(3) = statArray(3) + 1
            Case 510 To 549.999999
                statArray(4) = statArray(4) + 1
            Case 550 To 589.999999
                statArray(5) = statArray(5) + 1
            Case 590 To 629.999999
                statArray(6) = statArray(6) + 1
            Case 630 To 669.999999
                statArray(7) = statArray(7) + 1
            Case 670 To 709.999999
                statArray(8) = statArray(8) + 1
            Case 710 To 749.999999
                statArray(9) = statArray(9) + 1
            Case 750 To 789.999999
                statArray(10) = statArray(10) + 1
            Case 790 To 829.999999
                statArray(11) = statArray(11) + 1
            Case 830 To 869.999999
                statArray(12) = statArray(12) + 1
            Case 870 To 909.999999
                statArray(13) = statArray(13) + 1
            Case 910 To 949.999999
                statArray(14) = statArray(14) + 1
            Case Is >= 950
                statArray(15) = statArray(15) + 1
        End Select
    End If

    ' 更新字典
    dict(key) = statArray
    Set dict(key & "_students") = studentDict

    ' 返回统计数组
    ProcessStatistics = statArray
End Function

Sub 原始分数段统计()
    Dim wb As Workbook
    Dim wsScore As Worksheet
    Dim wsStat As Worksheet
    Dim lastRow As Long
    Dim i As Long, j As Long
    Dim grade As String
    Dim schoolDict As Object
    Dim classDict As Object
    Dim scoreArr() As Variant
    Dim statArr() As Variant
    Dim score As Variant ' 修改为 Variant 类型
    Dim statRow As Long
    Dim classItem As Variant
    Dim schoolItem As Variant
    Dim classInfo() As String
    Dim schoolInfo() As String
    Dim classStat() As Variant
    Dim schoolStat() As Variant
    Dim totalData() As Variant
    Dim classRankRange As Range
    Dim schoolRankRange As Range
    Dim summaryRows() As Long
    Dim classStudentDict As Object
    Dim schoolStudentDict As Object
    Dim student As Variant ' 声明 student 变量

    ' 设置工作簿和工作表
    Set wb = ThisWorkbook
    Set wsScore = wb.Sheets("成绩总表")
    Set wsStat = wb.Sheets("原始分数段统计")

    ' 清除第5行开始的所有单元格格式和内容
    wsStat.Rows("5:" & wsStat.Rows.Count).ClearContents
    wsStat.Rows("5:" & wsStat.Rows.Count).ClearFormats

    ' 获取要统计的年级
    grade = wsStat.Range("D2").value

    ' 获取成绩总表的最后一行
    lastRow = wsScore.Cells(wsScore.Rows.Count, 1).End(xlUp).Row

    ' 获取成绩总表的数据到数组
    scoreArr = wsScore.Range("A1:T" & lastRow).value

    ' 创建字典用于存储学校和班级的数据
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")

    ' 遍历成绩总表的数据
    For i = 2 To UBound(scoreArr)
        If scoreArr(i, 1) = grade Then
            Dim schoolNo As String
            Dim schoolName As String
            Dim className As String
            Dim studentName As String
            schoolNo = scoreArr(i, 2)
            schoolName = scoreArr(i, 3)
            className = scoreArr(i, 4)
            studentName = scoreArr(i, 6) ' 姓名在第6列（F列）
            score = scoreArr(i, 17) ' 总分在第17列（Q列）

            ' 检查 score 是否为数值
            If Not IsNumeric(score) Then
                score = ""
            End If

            ' 处理班级数据
            Dim classKey As String
            classKey = schoolNo & "|" & schoolName & "|" & className
            ' 创建唯一的学生标识符，使用行号和姓名组合
            Dim studentClassKey As String
            studentClassKey = i & "|" & studentName

            ' 使用辅助函数处理班级统计
            classStat = ProcessStatistics(classDict, classKey, studentClassKey, score)

            ' 处理学校数据
            Dim schoolKey As String
            schoolKey = schoolNo & "|" & schoolName
            ' 创建唯一的学生标识符，使用行号和姓名组合
            Dim studentSchoolKey As String
            studentSchoolKey = i & "|" & studentName

            ' 使用辅助函数处理学校统计
            schoolStat = ProcessStatistics(schoolDict, schoolKey, studentSchoolKey, score)
        End If
    Next i

    ' 初始化统计数组，改为 21 列
    ReDim statArr(1 To classDict.Count + schoolDict.Count + 1, 1 To 21)

    ' 填充学校统计数据，每个学校汇总跟在其班级后面
    Dim schoolClassCountDict As Object
    Set schoolClassCountDict = CreateObject("Scripting.Dictionary")
    For Each classItem In classDict.Keys
        If Not Right(classItem, 9) = "_students" Then
            classInfo = Split(classItem, "|")
            schoolKey = classInfo(0) & "|" & classInfo(1)
            If Not schoolClassCountDict.Exists(schoolKey) Then
                schoolClassCountDict(schoolKey) = 0
            End If
            schoolClassCountDict(schoolKey) = schoolClassCountDict(schoolKey) + 1
        End If
    Next classItem

    Dim currentRow As Long
    currentRow = 1
    Dim classRowsAdded As Long
    For Each schoolItem In schoolDict.Keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolInfo = Split(schoolItem, "|")
            schoolStat = schoolDict(schoolItem)
            classRowsAdded = 0
            ' 先输出该学校的班级数据
            For Each classItem In classDict.Keys
                If Not Right(classItem, 9) = "_students" Then
                    classInfo = Split(classItem, "|")
                    If classInfo(0) & "|" & classInfo(1) = schoolItem Then
                        classStat = classDict(classItem)
                        statArr(currentRow, 1) = classInfo(0)
                        statArr(currentRow, 2) = classInfo(1)
                        statArr(currentRow, 3) = classInfo(2)
                        For i = 4 To 20
                            statArr(currentRow, i) = classStat(i - 4)
                        Next i
                        If classStat(1) > 0 Then
                            statArr(currentRow, 20) = classStat(19) / classStat(1)
                        End If
                        currentRow = currentRow + 1
                        classRowsAdded = classRowsAdded + 1
                    End If
                End If
            Next classItem
            ' 输出该学校的汇总数据
            statArr(currentRow, 1) = schoolInfo(0)
            statArr(currentRow, 2) = schoolInfo(1) & "汇总"
            statArr(currentRow, 3) = ""
            For i = 4 To 20
                statArr(currentRow, i) = schoolStat(i - 4)
            Next i
            If schoolStat(1) > 0 Then
                statArr(currentRow, 20) = schoolStat(19) / schoolStat(1)
            End If
            currentRow = currentRow + 1
        End If
    Next schoolItem

    ' 计算全区汇总数据
    ReDim totalData(0 To 19)
    Dim allStudentsDict As Object
    Set allStudentsDict = CreateObject("Scripting.Dictionary")
    For Each schoolItem In schoolDict.Keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolStat = schoolDict(schoolItem)
            Set schoolStudentDict = schoolDict(schoolItem & "_students")
            For Each student In schoolStudentDict
                ' 使用原始的学生标识符，确保不会重复计算
                If Not allStudentsDict.Exists(student) Then
                    allStudentsDict(student) = 1
                End If
            Next student
            For i = 0 To 19
                totalData(i) = totalData(i) + schoolStat(i)
            Next i
        End If
    Next schoolItem
    totalData(0) = allStudentsDict.Count ' 全区实有人数
    statArr(currentRow, 1) = ""
    statArr(currentRow, 2) = "全区汇总"
    statArr(currentRow, 3) = ""
    For i = 4 To 20
        statArr(currentRow, i) = totalData(i - 4)
    Next i
    If totalData(1) > 0 Then
        statArr(currentRow, 20) = totalData(19) / totalData(1)
    End If

    ' 增加当前行计数，以便正确计算最后一行
    currentRow = currentRow + 1

    ' 填充统计数据到工作表
    wsStat.Range("A5").Resize(UBound(statArr), UBound(statArr, 2)).value = statArr

    ' 计算班级和学校排名
    Dim classRows() As Long
    Dim schoolRows() As Long
    Dim rowCount As Long

    ' 收集班级行号
    rowCount = 0
    ReDim classRows(0 To classDict.Count - 1)
    For i = 5 To 4 + UBound(statArr)
        ' 检查是否为班级行（非汇总行）
        If wsStat.Cells(i, 3).value <> "" Then
            classRows(rowCount) = i
            rowCount = rowCount + 1
        End If
    Next i

    ' 调整数组大小以匹配实际班级数量
    If rowCount > 0 And rowCount < UBound(classRows) + 1 Then
        ReDim Preserve classRows(0 To rowCount - 1)
    End If

    ' 收集学校汇总行号
    rowCount = 0
    ReDim schoolRows(0 To schoolDict.Count - 1)
    For i = 5 To 4 + UBound(statArr)
        ' 检查是否为学校汇总行（包含"汇总"字样）
        If wsStat.Cells(i, 2).value <> "" And wsStat.Cells(i, 3).value = "" And InStr(wsStat.Cells(i, 2).value, "汇总") > 0 And wsStat.Cells(i, 2).value <> "全区汇总" Then
            schoolRows(rowCount) = i
            rowCount = rowCount + 1
        End If
    Next i

    ' 调整数组大小以匹配实际学校数量
    If rowCount > 0 And rowCount < UBound(schoolRows) + 1 Then
        ReDim Preserve schoolRows(0 To rowCount - 1)
    End If

    ' 计算班级排名
    If UBound(classRows) >= 0 Then
        ' 创建班级平均分数组
        Dim classAvgScores() As Double
        ReDim classAvgScores(0 To UBound(classRows))

        ' 获取所有班级的平均分
        For i = 0 To UBound(classRows)
            classAvgScores(i) = CDbl(wsStat.Cells(classRows(i), 20).value)
        Next i

        ' 计算每个班级的排名
        For i = 0 To UBound(classRows)
            Dim classRank As Long
            classRank = 1

            ' 计算排名（比较当前班级与所有班级）
            For j = 0 To UBound(classRows)
                If classAvgScores(j) > classAvgScores(i) Then
                    classRank = classRank + 1
                End If
            Next j

            ' 设置排名值
            wsStat.Cells(classRows(i), 21).value = classRank
        Next i
    End If

    ' 计算学校排名
    If UBound(schoolRows) >= 0 Then
        ' 创建学校平均分数组
        Dim schoolAvgScores() As Double
        ReDim schoolAvgScores(0 To UBound(schoolRows))

        ' 获取所有学校的平均分
        For i = 0 To UBound(schoolRows)
            schoolAvgScores(i) = CDbl(wsStat.Cells(schoolRows(i), 20).value)
        Next i

        ' 计算每个学校的排名
        For i = 0 To UBound(schoolRows)
            Dim schoolRank As Long
            schoolRank = 1

            ' 计算排名（比较当前学校与所有学校）
            For j = 0 To UBound(schoolRows)
                If schoolAvgScores(j) > schoolAvgScores(i) Then
                    schoolRank = schoolRank + 1
                End If
            Next j

            ' 设置排名值
            wsStat.Cells(schoolRows(i), 21).value = schoolRank
        Next i
    End If

    ' 排名已经直接设置为值，无需转换

    ' 设置汇总行格式
    ReDim summaryRows(0 To schoolDict.Count)

    ' 使用已经收集的学校汇总行号
    For i = 0 To UBound(schoolRows)
        summaryRows(i) = schoolRows(i)
    Next i

    ' 处理全区汇总行
    ' 找到全区汇总行
    Dim districtSummaryRow As Long
    For i = 5 To 4 + UBound(statArr)
        If wsStat.Cells(i, 2).value = "全区汇总" Then
            districtSummaryRow = i
            Exit For
        End If
    Next i

    If districtSummaryRow > 0 Then
        ReDim Preserve summaryRows(0 To UBound(schoolRows) + 1)
        summaryRows(UBound(summaryRows)) = districtSummaryRow
    End If

    For i = LBound(summaryRows) To UBound(summaryRows)
        If summaryRows(i) > 0 And summaryRows(i) <= wsStat.Rows.Count Then ' 检查行号范围
            wsStat.Rows(summaryRows(i)).Font.Color = RGB(255, 0, 0)
            wsStat.Rows(summaryRows(i)).Font.Bold = True
            wsStat.Range(wsStat.Cells(summaryRows(i), 1), wsStat.Cells(summaryRows(i), 21)).Interior.Color = RGB(255, 255, 0)
            wsStat.Cells(summaryRows(i), 2).Resize(1, 2).Merge
        End If
    Next i

    ' 给所有数据单元格添加边框和设置格式
    ' 使用实际数据行数
    Dim lastDataRow As Long
    lastDataRow = 4 + currentRow - 1 ' 减1是因为currentRow已经在最后增加1

    ' 添加边框
    wsStat.Range("A5:U" & lastDataRow).Borders.LineStyle = xlContinuous

    ' 设置所有数据居中
    With wsStat.Range("A5:U" & lastDataRow)
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    ' 添加验证逻辑，确保实有人数大于等于参考人数
    Dim hasError As Boolean
    hasError = False

    ' 检查每一行的实有人数和参考人数
    For i = 5 To lastDataRow
        Dim actualCount As Long
        Dim participatingCount As Long

        actualCount = wsStat.Cells(i, 4).value ' 实有人数在第4列
        participatingCount = wsStat.Cells(i, 5).value ' 参考人数在第5列

        ' 如果实有人数小于参考人数，标记为错误
        If actualCount < participatingCount Then
            wsStat.Cells(i, 4).Interior.Color = RGB(255, 0, 0) ' 红色背景
            wsStat.Cells(i, 5).Interior.Color = RGB(255, 0, 0) ' 红色背景
            hasError = True
        End If
    Next i

    ' 如果有错误，显示警告消息
    If hasError Then
        MsgBox "警告：存在实有人数小于参考人数的情况，已用红色标记。请检查数据！", vbExclamation, "数据验证"
    End If

End Sub
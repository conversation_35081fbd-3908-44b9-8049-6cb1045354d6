"""
Core functionality for shutdown and task execution.
"""
import os
import time
import logging
import threading
import subprocess
from datetime import datetime, timedelta

import utils
from config import Config

logger = logging.getLogger("定时关机")

class ShutdownController:
    """Controller for shutdown and task execution."""
    
    def __init__(self, config):
        """Initialize the shutdown controller.
        
        Args:
            config: The application configuration.
        """
        self.config = config
        self.shutdown_thread = None
        self.shutdown_event = threading.Event()
        self.is_running = False
    
    def start_shutdown_timer(self):
        """Start the shutdown timer based on the configured shutdown type."""
        if self.is_running:
            self.stop_shutdown_timer()
        
        shutdown_type = self.config.get("shutdown_type")
        
        if shutdown_type == "time":
            self._start_shutdown_at_time()
        elif shutdown_type == "delay":
            self._start_shutdown_after_delay()
        elif shutdown_type == "boot":
            self._start_shutdown_after_boot()
        elif shutdown_type == "network":
            self._start_shutdown_on_low_network()
        elif shutdown_type == "idle":
            self._start_shutdown_on_idle()
        else:
            logger.error(f"Unknown shutdown type: {shutdown_type}")
            return False
        
        return True
    
    def stop_shutdown_timer(self):
        """Stop the shutdown timer."""
        if self.is_running and self.shutdown_thread is not None:
            self.shutdown_event.set()
            self.shutdown_thread.join(timeout=1.0)
            self.is_running = False
            self.shutdown_event.clear()
            
            # Cancel any scheduled system shutdown
            os.system("shutdown -a")
            
            logger.info("Shutdown timer stopped")
            return True
        
        return False
    
    def _start_shutdown_at_time(self):
        """Start a timer to shutdown at a specific time."""
        target_time_str = self.config.get("shutdown_time")
        target_time = utils.parse_time(target_time_str)
        
        if target_time is None:
            logger.error(f"Invalid shutdown time: {target_time_str}")
            return False
        
        now = datetime.now()
        target_datetime = datetime.combine(now.date(), target_time)
        
        # If the target time is in the past, schedule for tomorrow
        if target_datetime < now:
            target_datetime += timedelta(days=1)
        
        delay_seconds = (target_datetime - now).total_seconds()
        
        self.shutdown_thread = threading.Thread(
            target=self._shutdown_after_delay_thread,
            args=(delay_seconds,),
            daemon=True
        )
        self.shutdown_thread.start()
        self.is_running = True
        
        logger.info(f"Scheduled shutdown at {target_time_str} (in {utils.format_time(delay_seconds)})")
        return True
    
    def _start_shutdown_after_delay(self):
        """Start a timer to shutdown after a delay."""
        delay_seconds = self.config.get("shutdown_delay")
        
        self.shutdown_thread = threading.Thread(
            target=self._shutdown_after_delay_thread,
            args=(delay_seconds,),
            daemon=True
        )
        self.shutdown_thread.start()
        self.is_running = True
        
        logger.info(f"Scheduled shutdown after {utils.format_time(delay_seconds)}")
        return True
    
    def _start_shutdown_after_boot(self):
        """Start a timer to shutdown after a certain boot time."""
        boot_seconds = self.config.get("shutdown_boot_time")
        uptime = utils.get_uptime()
        
        if uptime >= boot_seconds:
            logger.info(f"System uptime ({utils.format_time(uptime)}) already exceeds boot time limit ({utils.format_time(boot_seconds)})")
            self._execute_task()
            return True
        
        delay_seconds = boot_seconds - uptime
        
        self.shutdown_thread = threading.Thread(
            target=self._shutdown_after_delay_thread,
            args=(delay_seconds,),
            daemon=True
        )
        self.shutdown_thread.start()
        self.is_running = True
        
        logger.info(f"Scheduled shutdown after boot time of {utils.format_time(boot_seconds)} (in {utils.format_time(delay_seconds)})")
        return True
    
    def _start_shutdown_on_low_network(self):
        """Start monitoring network speed and shutdown when it's below threshold for a duration."""
        threshold_kbps = self.config.get("network_threshold")
        duration_seconds = self.config.get("network_duration")
        
        self.shutdown_thread = threading.Thread(
            target=self._shutdown_on_low_network_thread,
            args=(threshold_kbps, duration_seconds),
            daemon=True
        )
        self.shutdown_thread.start()
        self.is_running = True
        
        logger.info(f"Monitoring network speed (threshold: {threshold_kbps} KB/s, duration: {utils.format_time(duration_seconds)})")
        return True
    
    def _start_shutdown_on_idle(self):
        """Start monitoring system idle time and shutdown when idle for a duration."""
        idle_seconds = self.config.get("idle_time")
        
        self.shutdown_thread = threading.Thread(
            target=self._shutdown_on_idle_thread,
            args=(idle_seconds,),
            daemon=True
        )
        self.shutdown_thread.start()
        self.is_running = True
        
        logger.info(f"Monitoring system idle time (threshold: {utils.format_time(idle_seconds)})")
        return True
    
    def _shutdown_after_delay_thread(self, delay_seconds):
        """Thread function to shutdown after a delay."""
        end_time = time.time() + delay_seconds
        
        while time.time() < end_time and not self.shutdown_event.is_set():
            time.sleep(1.0)
        
        if not self.shutdown_event.is_set():
            logger.info("Delay timer expired, executing task")
            self._execute_task()
        
        self.is_running = False
    
    def _shutdown_on_low_network_thread(self, threshold_kbps, duration_seconds):
        """Thread function to monitor network speed and shutdown when below threshold."""
        start_time = None
        
        while not self.shutdown_event.is_set():
            network_speed = utils.get_network_speed()
            
            if network_speed < threshold_kbps:
                if start_time is None:
                    start_time = time.time()
                    logger.debug(f"Network speed below threshold: {network_speed:.2f} KB/s")
                
                elapsed = time.time() - start_time
                if elapsed >= duration_seconds:
                    logger.info(f"Network speed below threshold for {utils.format_time(duration_seconds)}, executing task")
                    self._execute_task()
                    break
            else:
                start_time = None
            
            time.sleep(1.0)
        
        self.is_running = False
    
    def _shutdown_on_idle_thread(self, idle_seconds):
        """Thread function to monitor system idle time and shutdown when idle for a duration."""
        import win32api
        import win32gui
        
        while not self.shutdown_event.is_set():
            # Get the idle time in milliseconds
            idle_time = (win32api.GetTickCount() - win32gui.GetLastInputInfo()) / 1000.0
            
            if idle_time >= idle_seconds:
                logger.info(f"System idle for {utils.format_time(idle_seconds)}, executing task")
                self._execute_task()
                break
            
            time.sleep(1.0)
        
        self.is_running = False
    
    def _execute_task(self):
        """Execute the configured task."""
        task_type = self.config.get("task_type")
        
        if task_type == "shutdown":
            self._shutdown_computer()
        elif task_type == "restart":
            self._restart_computer()
        elif task_type == "lock":
            self._lock_computer()
        elif task_type == "remind":
            self._show_reminder()
        elif task_type == "open_file":
            self._open_file()
        elif task_type == "execute":
            self._execute_program()
        elif task_type == "close":
            self._close_program()
        else:
            logger.error(f"Unknown task type: {task_type}")
    
    def _shutdown_computer(self):
        """Shutdown the computer."""
        logger.info("Shutting down computer")
        os.system("shutdown /s /t 60 /c \"定时关机: 系统将在60秒后关闭\"")
    
    def _restart_computer(self):
        """Restart the computer."""
        logger.info("Restarting computer")
        os.system("shutdown /r /t 60 /c \"定时关机: 系统将在60秒后重启\"")
    
    def _lock_computer(self):
        """Lock the computer."""
        logger.info("Locking computer")
        os.system("rundll32.exe user32.dll,LockWorkStation")
    
    def _show_reminder(self):
        """Show a reminder message."""
        message = self.config.get("task_message")
        logger.info(f"Showing reminder: {message}")
        
        # Use PowerShell to display a notification
        ps_command = f'powershell -Command "& {{Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show(\'{message}\', \'定时关机\', [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)}}"'
        subprocess.Popen(ps_command, shell=True)
    
    def _open_file(self):
        """Open a file."""
        file_path = self.config.get("task_file_path")
        logger.info(f"Opening file: {file_path}")
        
        try:
            os.startfile(file_path)
        except Exception as e:
            logger.error(f"Error opening file: {e}")
    
    def _execute_program(self):
        """Execute a program."""
        program_path = self.config.get("task_program_path")
        logger.info(f"Executing program: {program_path}")
        
        try:
            subprocess.Popen(program_path, shell=True)
        except Exception as e:
            logger.error(f"Error executing program: {e}")
    
    def _close_program(self):
        """Close a program."""
        program_name = self.config.get("task_program_name")
        logger.info(f"Closing program: {program_name}")
        
        utils.kill_process(program_name)

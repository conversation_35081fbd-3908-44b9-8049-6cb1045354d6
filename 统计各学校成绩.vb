Sub 成绩汇总统计()
    Dim ws As Worksheet
    Dim lastRow As Long, lastCol As Long
    Dim i As <PERSON>, j <PERSON>, k <PERSON> Long
    Dim schoolArr() As String, gradeArr() As String
    Dim schoolCount As Long, gradeCount As Long
    Dim dataRange As Range
    Dim totalScoreCol As Long, chineseCol <PERSON>, mathCol <PERSON>, englishCol <PERSON>, scienceCol <PERSON> Long, moralCol As Long
    Dim gradeCol As Long, schoolCol As Long, nameCol As Long
    Dim resultRow As Long
    Dim currentGrade As String, currentSchool As String
    Dim totalStudents As Long, excellentCount As <PERSON>, passCount As Long, lowCount As Long
    Dim totalScore As Double, maxScore As Double, minScore As Double
    Dim chineseTotal As Double, mathTotal As Double, englishTotal As Double, scienceTotal As Double, moralTotal As Double
    Dim resultWs As Worksheet
    
    
    ' 确保所有变量都已定义
'    Dim ws As Worksheet
'    Dim k As Long
'    Dim moralCol As Long
    
    ' 设置变量值
    Set ws = ThisWorkbook.Sheets("Sheet1") ' 替换为你的工作表名
    k = 1 ' 替换为你的行索引
    moralCol = 2 ' 替换为你的列索引
    
    ' 添加错误处理
    On Error Resume Next
    If Not IsError(ws.Cells(k, moralCol).Value) Then
        If Len(Trim(CStr(ws.Cells(k, moralCol).Value))) > 0 Then
            ' 你的代码
        End If
    End If
    On Error GoTo 0




    ' 设置工作表
    Set ws = Sheets("成绩汇总表")
    Set resultWs = Sheets("学校统计") ' 假设结果输出在同一工作表

    ' 找到最后一行和最后一列
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).column

    ' 找到关键列的位置
    For i = 1 To lastCol
        Select Case ws.Cells(1, i).Value
            Case "年级"
                gradeCol = i
            Case "学校"
                schoolCol = i
            Case "姓名"
                nameCol = i
            Case "语文"
                chineseCol = i
            Case "数学"
                mathCol = i
            Case "英语"
                englishCol = i
            Case "科学"
                scienceCol = i
            Case "道德与法治"
                moralCol = i
            Case "总分"
                totalScoreCol = i
        End Select
    Next i

    ' 使用数组直接收集唯一的学校和年级
    ' 首先，创建临时数组来存储所有可能的值
    Dim tempSchools() As String
    Dim tempGrades() As String
    ReDim tempSchools(1 To lastRow)
    ReDim tempGrades(1 To lastRow)

    schoolCount = 0
    gradeCount = 0

    ' 收集所有学校和年级
    For i = 4 To lastRow
        Dim schoolFound As Boolean, gradeFound As Boolean
        schoolFound = False
        gradeFound = False

        ' 检查学校是否已经在数组中 - 使用Len函数检查是否为空
        If Len(Trim(ws.Cells(i, schoolCol).Value)) > 0 Then
            For j = 1 To schoolCount
                If tempSchools(j) = ws.Cells(i, schoolCol).Value Then
                    schoolFound = True
                    Exit For
                End If
            Next j

            ' 如果学校不在数组中，添加它
            If Not schoolFound Then
                schoolCount = schoolCount + 1
                tempSchools(schoolCount) = ws.Cells(i, schoolCol).Value
            End If
        End If

        ' 检查年级是否已经在数组中 - 使用Len函数检查是否为空
        If Len(Trim(ws.Cells(i, gradeCol).Value)) > 0 Then
            For j = 1 To gradeCount
                If tempGrades(j) = ws.Cells(i, gradeCol).Value Then
                    gradeFound = True
                    Exit For
                End If
            Next j

            ' 如果年级不在数组中，添加它
            If Not gradeFound Then
                gradeCount = gradeCount + 1
                tempGrades(gradeCount) = ws.Cells(i, gradeCol).Value
            End If
        End If
    Next i

    ' 重新调整数组大小以匹配实际的唯一值数量
    ReDim schoolArr(1 To schoolCount)
    ReDim gradeArr(1 To gradeCount)

    ' 复制唯一值到最终数组
    For i = 1 To schoolCount
        schoolArr(i) = tempSchools(i)
    Next i

    For i = 1 To gradeCount
        gradeArr(i) = tempGrades(i)
    Next i

    ' 排序年级数组（假设年级是数字或可比较的字符串）
    For i = 1 To gradeCount - 1
        For j = i + 1 To gradeCount
            If gradeArr(i) > gradeArr(j) Then
                currentGrade = gradeArr(i)
                gradeArr(i) = gradeArr(j)
                gradeArr(j) = currentGrade
            End If
        Next j
    Next i

    ' 结果起始行（假设表头已存在）
    resultRow = 4

    ' 对每个年级进行统计
    For i = 1 To gradeCount
        currentGrade = gradeArr(i)

        ' 创建一个临时数组来存储每个学校的统计结果
        Dim schoolStats() As Variant
        ReDim schoolStats(1 To schoolCount + 1, 1 To 24) ' +1 是为了全区统计

        ' 对每个学校进行统计
        For j = 1 To schoolCount + 1
            If j <= schoolCount Then
                currentSchool = schoolArr(j)
            Else
                currentSchool = "全区"
            End If

            ' 初始化统计变量
            totalStudents = 0
            totalScore = 0
            excellentCount = 0
            passCount = 0
            lowCount = 0
            maxScore = 0
            minScore = 9999
            chineseTotal = 0
            mathTotal = 0
            englishTotal = 0
            scienceTotal = 0
            moralTotal = 0

            ' 统计数据
            For k = 4 To lastRow
                ' 如果是全区统计或者学校匹配
                If (j > schoolCount Or ws.Cells(k, schoolCol).Value = currentSchool) And ws.Cells(k, gradeCol).Value = currentGrade Then
                    totalStudents = totalStudents + 1

                    ' 总分统计 - 使用Len函数检查是否为空
 ' 总分统计
If Len(Trim(ws.Cells(k, totalScoreCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, totalScoreCol).Value) Then
        Dim score As Double
        score = CDbl(ws.Cells(k, totalScoreCol).Value)
        
        totalScore = totalScore + score
        
        If score >= 425 Then
            excellentCount = excellentCount + 1
        End If
        
        If score >= 300 Then
            passCount = passCount + 1
        End If
        
        If score < 200 Then
            lowCount = lowCount + 1
        End If
        
        If score > maxScore Then
            maxScore = score
        End If
        
        If score < minScore Then
            minScore = score
        End If
    End If
End If

' 各科目统计
If Len(Trim(ws.Cells(k, chineseCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, chineseCol).Value) Then
        chineseTotal = chineseTotal + CDbl(ws.Cells(k, chineseCol).Value)
    End If
End If

If Len(Trim(ws.Cells(k, mathCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, mathCol).Value) Then
        mathTotal = mathTotal + CDbl(ws.Cells(k, mathCol).Value)
    End If
End If

If Len(Trim(ws.Cells(k, englishCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, englishCol).Value) Then
        englishTotal = englishTotal + CDbl(ws.Cells(k, englishCol).Value)
    End If
End If

If Len(Trim(ws.Cells(k, scienceCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, scienceCol).Value) Then
        scienceTotal = scienceTotal + CDbl(ws.Cells(k, scienceCol).Value)
    End If
End If

If Len(Trim(ws.Cells(k, moralCol).Value)) > 0 Then
    If IsNumeric(ws.Cells(k, moralCol).Value) Then
        moralTotal = moralTotal + CDbl(ws.Cells(k, moralCol).Value)
    End If
End If

                End If
            Next k

            ' 计算平均分
            Dim avgTotal As Double, avgChinese As Double, avgMath As Double, avgEnglish As Double, avgScience As Double, avgMoral As Double

            If totalStudents > 0 Then
                avgTotal = totalScore / totalStudents
                avgChinese = chineseTotal / totalStudents
                avgMath = mathTotal / totalStudents
                avgEnglish = englishTotal / totalStudents
                avgScience = scienceTotal / totalStudents
                avgMoral = moralTotal / totalStudents
            Else
                avgTotal = 0
                avgChinese = 0
                avgMath = 0
                avgEnglish = 0
                avgScience = 0
                avgMoral = 0
            End If

            ' 如果没有学生，最低分设为0
            If minScore = 9999 Then
                minScore = 0
            End If

            ' 存储统计结果
            schoolStats(j, 1) = currentGrade
            schoolStats(j, 2) = currentSchool
            schoolStats(j, 3) = totalStudents
            schoolStats(j, 4) = avgTotal
            schoolStats(j, 5) = 0 ' 排名，稍后填充
            schoolStats(j, 6) = excellentCount
            schoolStats(j, 7) = IIf(totalStudents > 0, excellentCount / totalStudents * 100, 0) ' 优秀比例%
            schoolStats(j, 8) = passCount
            schoolStats(j, 9) = IIf(totalStudents > 0, passCount / totalStudents * 100, 0) ' 合格比例%
            schoolStats(j, 10) = lowCount
            schoolStats(j, 11) = IIf(totalStudents > 0, lowCount / totalStudents * 100, 0) ' 低分比例%
            schoolStats(j, 12) = maxScore
            schoolStats(j, 13) = minScore
            schoolStats(j, 14) = (avgChinese + avgMath + avgEnglish + avgScience + avgMoral) / 5 ' 各科平均分
            schoolStats(j, 15) = avgChinese
            schoolStats(j, 16) = 0 ' 语文排名，稍后填充
            schoolStats(j, 17) = avgMath
            schoolStats(j, 18) = 0 ' 数学排名，稍后填充
            schoolStats(j, 19) = avgEnglish
            schoolStats(j, 20) = 0 ' 英语排名，稍后填充
            schoolStats(j, 21) = avgScience
            schoolStats(j, 22) = 0 ' 科学排名，稍后填充
            schoolStats(j, 23) = avgMoral
            schoolStats(j, 24) = 0 ' 道法排名，稍后填充
        Next j

        ' 计算排名（育英学校不参与排名）
        ' 总分排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                Dim rank As Long
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 4) > schoolStats(j, 4) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 5) = rank
            End If
        Next j

        ' 语文排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 15) > schoolStats(j, 15) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 16) = rank
            End If
        Next j

        ' 数学排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 17) > schoolStats(j, 17) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 18) = rank
            End If
        Next j

        ' 英语排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 19) > schoolStats(j, 19) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 20) = rank
            End If
        Next j

        ' 科学排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 21) > schoolStats(j, 21) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 22) = rank
            End If
        Next j

        ' 道法排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 23) > schoolStats(j, 23) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 24) = rank
            End If
        Next j

        ' 输出结果
        For j = 1 To schoolCount + 1
            ' 年级
            resultWs.Cells(resultRow, 1).Value = schoolStats(j, 1)
            ' 学校
            resultWs.Cells(resultRow, 2).Value = schoolStats(j, 2)
            ' 参考人数
            resultWs.Cells(resultRow, 3).Value = schoolStats(j, 3)
            ' 总分平均分
            resultWs.Cells(resultRow, 4).Value = Round(schoolStats(j, 4), 2)
            ' 总分排名
            resultWs.Cells(resultRow, 5).Value = schoolStats(j, 5)
            ' 总分优秀人数
            resultWs.Cells(resultRow, 6).Value = schoolStats(j, 6)
            ' 总分优秀比例%
            resultWs.Cells(resultRow, 7).Value = Round(schoolStats(j, 7), 2)
            ' 总分合格人数
            resultWs.Cells(resultRow, 8).Value = schoolStats(j, 8)
            ' 总分合格比例%
            resultWs.Cells(resultRow, 9).Value = Round(schoolStats(j, 9), 2)
            ' 总分低分人数
            resultWs.Cells(resultRow, 10).Value = schoolStats(j, 10)
            ' 总分低分比例%
            resultWs.Cells(resultRow, 11).Value = Round(schoolStats(j, 11), 2)
            ' 总分最高分
            resultWs.Cells(resultRow, 12).Value = schoolStats(j, 12)
            ' 总分最低分
            resultWs.Cells(resultRow, 13).Value = schoolStats(j, 13)
            ' 各科平均分
            resultWs.Cells(resultRow, 14).Value = Round(schoolStats(j, 14), 2)
            ' 语文平均分
            resultWs.Cells(resultRow, 15).Value = Round(schoolStats(j, 15), 2)
            ' 语文平均分排名
            resultWs.Cells(resultRow, 16).Value = schoolStats(j, 16)
            ' 数学平均分
            resultWs.Cells(resultRow, 17).Value = Round(schoolStats(j, 17), 2)
            ' 数学平均分排名
            resultWs.Cells(resultRow, 18).Value = schoolStats(j, 18)
            ' 英语平均分
            resultWs.Cells(resultRow, 19).Value = Round(schoolStats(j, 19), 2)
            ' 英语平均分排名
            resultWs.Cells(resultRow, 20).Value = schoolStats(j, 20)
            ' 科学平均分
            resultWs.Cells(resultRow, 21).Value = Round(schoolStats(j, 21), 2)
            ' 科学平均分排名
            resultWs.Cells(resultRow, 22).Value = schoolStats(j, 22)
            ' 道法平均分
            resultWs.Cells(resultRow, 23).Value = Round(schoolStats(j, 23), 2)
            ' 道法平均分排名
            resultWs.Cells(resultRow, 24).Value = schoolStats(j, 24)
            
            ' 增加行号
            resultRow = resultRow + 1
        Next j
        
        ' 在每个年级统计后添加一个空行
        resultRow = resultRow + 1
    Next i
    
    ' 格式化结果 - 使用WPS兼容的方式
    Dim formatRange As Range
    Set formatRange = resultWs.Range(resultWs.Cells(4, 1), resultWs.Cells(resultRow - 1, 24))
    formatRange.Borders.LineStyle = xlContinuous
    formatRange.HorizontalAlignment = xlCenter
    formatRange.VerticalAlignment = xlCenter
    
    ' 设置百分比格式 - 使用WPS兼容的方式
    resultWs.Range(resultWs.Cells(4, 7), resultWs.Cells(resultRow - 1, 7)).NumberFormat = "0.00\%"
    resultWs.Range(resultWs.Cells(4, 9), resultWs.Cells(resultRow - 1, 9)).NumberFormat = "0.00\%"
    resultWs.Range(resultWs.Cells(4, 11), resultWs.Cells(resultRow - 1, 11)).NumberFormat = "0.00\%"
    
    MsgBox "统计完成！", vbInformation
End Sub



Option Explicit

Sub 统计班级成绩_完整版()
    ' --- 变量声明 ---
    Dim wb As Workbook
    Dim ws成绩汇总 As Worksheet, ws任课教师 As Worksheet, ws班级 As Worksheet
    Dim i As Long, j <PERSON>, lastRow As Long
    Dim 年级 As String
    Dim dict班级数据 As Object
    Dim dict教师 As Object
    Dim startTime As Double
    Dim teacherDataArr As Variant
    Dim scoreDataArr As Variant
    Dim outputRow As Long
    Dim classIndex As Long
    Dim arr班级平均分() As Variant ' 用于存储各班级各科平均分以进行排名
    Dim 班级key As Variant

    ' --- 常量定义 ---
    ' 成绩汇总表列索引
    Const COL_SUM_序号 As Integer = 1
    Const COL_SUM_学校 As Integer = 2
    Const COL_SUM_校点 As Integer = 3
    Const COL_SUM_年级 As Integer = 4
    Const COL_SUM_班级 As Integer = 5
    Const COL_SUM_准考证号 As Integer = 6
    Const COL_SUM_姓名 As Integer = 7
    Const COL_SUM_语文听写 As Integer = 8
    Const COL_SUM_语文 As Integer = 9
    Const COL_SUM_数学 As Integer = 10
    Const COL_SUM_英语 As Integer = 11
    Const COL_SUM_科学 As Integer = 12
    Const COL_SUM_道德与法治 As Integer = 13
    Const COL_SUM_总分五科 As Integer = 22
    
    ' 任课教师表列索引
    Const COL_TEA_学校 As Integer = 1
    Const COL_TEA_校点 As Integer = 2
    Const COL_TEA_年级 As Integer = 3
    Const COL_TEA_班级 As Integer = 4
    Const COL_TEA_语文听写 As Integer = 5
    Const COL_TEA_语文 As Integer = 6
    Const COL_TEA_数学 As Integer = 7
    Const COL_TEA_英语 As Integer = 8
    Const COL_TEA_科学 As Integer = 9
    Const COL_TEA_道法 As Integer = 10
    
    ' 班级表列索引
    Const TGT_COL_序号 As Integer = 1
    Const TGT_COL_学校 As Integer = 2
    Const TGT_COL_校点 As Integer = 3
    Const TGT_COL_班级 As Integer = 4
    Const TGT_COL_实考人数 As Integer = 5
    Const TGT_COL_语文平均分 As Integer = 6
    Const TGT_COL_语文年级名次 As Integer = 7
    Const TGT_COL_语文任课教师 As Integer = 8
    Const TGT_COL_数学平均分 As Integer = 9
    Const TGT_COL_数学年级名次 As Integer = 10
    Const TGT_COL_数学任课教师 As Integer = 11
    Const TGT_COL_英语平均分 As Integer = 12
    Const TGT_COL_英语年级名次 As Integer = 13
    Const TGT_COL_英语任课教师 As Integer = 14
    Const TGT_COL_科学平均分 As Integer = 15
    Const TGT_COL_科学年级名次 As Integer = 16
    Const TGT_COL_科学任课教师 As Integer = 17
    Const TGT_COL_道法平均分 As Integer = 18
    Const TGT_COL_道法年级名次 As Integer = 19
    Const TGT_COL_道法任课教师 As Integer = 20
    Const TGT_COL_总分平均 As Integer = 21
    Const TGT_COL_总分年级名次 As Integer = 22
    
    ' --- 初始化 ---
    startTime = Timer
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    Set dict班级数据 = CreateObject("Scripting.Dictionary")
    Set dict教师 = CreateObject("Scripting.Dictionary")
    Set wb = ThisWorkbook
    
    On Error GoTo ErrorHandler
    Set ws成绩汇总 = wb.Sheets("成绩汇总表")
    Set ws任课教师 = wb.Sheets("任课教师")
    Set ws班级 = wb.Sheets("班级")
    On Error GoTo 0

    ' 1. 获取目标年级
    年级 = Trim(CStr(ws班级.Range("C1").Value))
    If 年级 = "" Then
        MsgBox "请在 '班级' 表的 C1 单元格输入要统计的年级。", vbExclamation, "输入错误"
        GoTo CleanUp
    End If

    ' 2. 读取任课教师数据
    lastRow = ws任课教师.Cells(ws任课教师.Rows.count, COL_TEA_学校).End(xlUp).row
    If lastRow >= 2 Then ' 至少有表头和一行数据
        teacherDataArr = ws任课教师.Range(ws任课教师.Cells(1, 1), ws任课教师.Cells(lastRow, COL_TEA_道法)).Value2 ' 读取到数组以提高效率

        For i = 2 To UBound(teacherDataArr, 1) ' 从第二行数据开始
            If Trim(CStr(teacherDataArr(i, COL_TEA_年级))) = 年级 Then
                Dim key As String
                key = Trim(CStr(teacherDataArr(i, COL_TEA_学校))) & "_" & _
                      Trim(CStr(teacherDataArr(i, COL_TEA_校点))) & "_" & _
                      Trim(CStr(teacherDataArr(i, COL_TEA_班级)))

                If Not dict教师.Exists(key) Then
                    On Error Resume Next
                    Dim teacherInfo As Object
                    Set teacherInfo = CreateObject("Scripting.Dictionary")
                    If teacherInfo Is Nothing Then
                        MsgBox "创建教师信息字典失败，请检查系统是否支持Scripting.Dictionary", vbExclamation
                        Exit Sub
                    End If
                    
                    ' 确保键值不为空
                    Dim 语文教师 As String: 语文教师 = Trim(CStr(teacherDataArr(i, COL_TEA_语文)))
                    Dim 数学教师 As String: 数学教师 = Trim(CStr(teacherDataArr(i, COL_TEA_数学)))
                    Dim 英语教师 As String: 英语教师 = Trim(CStr(teacherDataArr(i, COL_TEA_英语)))
                    Dim 科学教师 As String: 科学教师 = Trim(CStr(teacherDataArr(i, COL_TEA_科学)))
                    Dim 道法教师 As String: 道法教师 = Trim(CStr(teacherDataArr(i, COL_TEA_道法)))
                    
                    If 语文教师 <> "" Then teacherInfo("语文") = 语文教师
                    If 数学教师 <> "" Then teacherInfo("数学") = 数学教师
                    If 英语教师 <> "" Then teacherInfo("英语") = 英语教师
                    If 科学教师 <> "" Then teacherInfo("科学") = 科学教师
                    If 道法教师 <> "" Then teacherInfo("道法") = 道法教师
                    
                    If Err.Number = 0 Then
                        dict教师.Add key, teacherInfo
                    Else
                        MsgBox "添加教师信息时出错: " & Err.Description, vbExclamation
                        Exit Sub
                    End If
                    On Error GoTo 0
                End If
            End If
        Next i
    End If

    ' 3. 统计成绩数据
    lastRow = ws成绩汇总.Cells(ws成绩汇总.Rows.count, COL_SUM_序号).End(xlUp).row
    If lastRow < 2 Then
        MsgBox "成绩汇总表没有数据。", vbInformation
        GoTo CleanUp
    End If
    scoreDataArr = ws成绩汇总.Range(ws成绩汇总.Cells(1, 1), ws成绩汇总.Cells(lastRow, COL_SUM_总分五科)).Value2

    For i = 2 To UBound(scoreDataArr, 1)
        If Trim(CStr(scoreDataArr(i, COL_SUM_年级))) = 年级 Then
            班级key = Trim(CStr(scoreDataArr(i, COL_SUM_学校))) & "_" & _
                     Trim(CStr(scoreDataArr(i, COL_SUM_校点))) & "_" & _
                     Trim(CStr(scoreDataArr(i, COL_SUM_班级)))

            If Not dict班级数据.Exists(班级key) Then
                On Error Resume Next
                Dim classDetail As Object
                Set classDetail = CreateObject("Scripting.Dictionary")
                If classDetail Is Nothing Then
                    MsgBox "创建班级数据字典失败", vbExclamation
                    Exit Sub
                End If
                
                ' 确保键值有效
                Dim 学校 As String: 学校 = Trim(CStr(scoreDataArr(i, COL_SUM_学校)))
                Dim 校点 As String: 校点 = Trim(CStr(scoreDataArr(i, COL_SUM_校点)))
                Dim 班级 As String: 班级 = Trim(CStr(scoreDataArr(i, COL_SUM_班级)))
                
                With classDetail
                    If 学校 <> "" Then .Add "学校", 学校
                    If 校点 <> "" Then .Add "校点", 校点
                    If 班级 <> "" Then .Add "班级", 班级
                    .Add "人数", 0
                    .Add "语文总分", 0# ' 使用 # 显式声明为 Double
                    .Add "数学总分", 0#
                    .Add "英语总分", 0#
                    .Add "科学总分", 0#
                    .Add "道法总分", 0#
                    .Add "总分（五科）合计", 0# ' 对应成绩汇总表的总分列
                    .Add "语文参考人数", 0
                    .Add "数学参考人数", 0
                    .Add "英语参考人数", 0
                    .Add "科学参考人数", 0
                    .Add "道法参考人数", 0
                End With
                
                If Err.Number = 0 Then
                    dict班级数据.Add 班级key, classDetail
                Else
                    MsgBox "添加班级数据时出错: " & Err.Description, vbExclamation
                    Exit Sub
                End If
                On Error GoTo 0
            End If

            With dict班级数据(班级key)
                .Item("人数") = .Item("人数") + 1
                ' 确保分数是数字，如果不是则按0处理或给出警告
                Dim 语文分数 As Double: 语文分数 = Val(scoreDataArr(i, COL_SUM_语文))
                Dim 数学分数 As Double: 数学分数 = Val(scoreDataArr(i, COL_SUM_数学))
                Dim 英语分数 As Double: 英语分数 = Val(scoreDataArr(i, COL_SUM_英语))
                Dim 科学分数 As Double: 科学分数 = Val(scoreDataArr(i, COL_SUM_科学))
                Dim 道法分数 As Double: 道法分数 = Val(scoreDataArr(i, COL_SUM_道德与法治))
                
                .Item("语文总分") = .Item("语文总分") + 语文分数
                If 语文分数 > 0 Then .Item("语文参考人数") = .Item("语文参考人数") + 1
                
                .Item("数学总分") = .Item("数学总分") + 数学分数
                If 数学分数 > 0 Then .Item("数学参考人数") = .Item("数学参考人数") + 1
                
                .Item("英语总分") = .Item("英语总分") + 英语分数
                If 英语分数 > 0 Then .Item("英语参考人数") = .Item("英语参考人数") + 1
                
                .Item("科学总分") = .Item("科学总分") + 科学分数
                If 科学分数 > 0 Then .Item("科学参考人数") = .Item("科学参考人数") + 1
                
                .Item("道法总分") = .Item("道法总分") + 道法分数
                If 道法分数 > 0 Then .Item("道法参考人数") = .Item("道法参考人数") + 1
                
                .Item("总分（五科）合计") = .Item("总分（五科）合计") + Val(scoreDataArr(i, COL_SUM_总分五科))
            End With
        End If
    Next i

    If dict班级数据.count = 0 Then
        MsgBox "在成绩汇总表中未找到年级 '" & 年级 & "' 的数据。", vbInformation
        GoTo CleanUp
    End If

    ' 4. 计算全区统计数据
    Dim 全区参考人数 As Long
    Dim 全区语文总分 As Double, 全区数学总分 As Double, 全区英语总分 As Double, 全区科学总分 As Double, 全区道法总分 As Double
    Dim 全区语文参考人数 As Long, 全区数学参考人数 As Long, 全区英语参考人数 As Long, 全区科学参考人数 As Long, 全区道法参考人数 As Long
    
    For Each 班级key In dict班级数据.Keys
        With dict班级数据(班级key)
            全区参考人数 = 全区参考人数 + .Item("人数")
            全区语文总分 = 全区语文总分 + .Item("语文总分")
            全区数学总分 = 全区数学总分 + .Item("数学总分")
            全区英语总分 = 全区英语总分 + .Item("英语总分")
            全区科学总分 = 全区科学总分 + .Item("科学总分")
            全区道法总分 = 全区道法总分 + .Item("道法总分")
            全区语文参考人数 = 全区语文参考人数 + .Item("语文参考人数")
            全区数学参考人数 = 全区数学参考人数 + .Item("数学参考人数")
            全区英语参考人数 = 全区英语参考人数 + .Item("英语参考人数")
            全区科学参考人数 = 全区科学参考人数 + .Item("科学参考人数")
            全区道法参考人数 = 全区道法参考人数 + .Item("道法参考人数")
        End With
    Next
    
    ' 5. 清空原有数据并准备写入班级表
    outputRow = 4 ' 数据从第四行开始
    If ws班级.Cells(ws班级.Rows.count, TGT_COL_序号).End(xlUp).row >= outputRow Then
        ws班级.Range(ws班级.Cells(outputRow, TGT_COL_序号), ws班级.Cells(ws班级.Rows.count, TGT_COL_总分年级名次)).ClearContents
    End If
    
    ' 写入全区统计数据
    ws班级.Cells(2, TGT_COL_序号).Value = "全区统计"
    ws班级.Cells(2, TGT_COL_实考人数).Value = 全区参考人数
    ws班级.Cells(2, TGT_COL_语文平均分).Value = IIf(全区语文参考人数 > 0, Round(全区语文总分 / 全区语文参考人数, 2), 0)
    ws班级.Cells(2, TGT_COL_数学平均分).Value = IIf(全区数学参考人数 > 0, Round(全区数学总分 / 全区数学参考人数, 2), 0)
    ws班级.Cells(2, TGT_COL_英语平均分).Value = IIf(全区英语参考人数 > 0, Round(全区英语总分 / 全区英语参考人数, 2), 0)
    ws班级.Cells(2, TGT_COL_科学平均分).Value = IIf(全区科学参考人数 > 0, Round(全区科学总分 / 全区科学参考人数, 2), 0)
    ws班级.Cells(2, TGT_COL_道法平均分).Value = IIf(全区道法参考人数 > 0, Round(全区道法总分 / 全区道法参考人数, 2), 0)
    
    ' 设置数字格式
    ws班级.Cells(2, TGT_COL_语文平均分).NumberFormat = "0.00"
    ws班级.Cells(2, TGT_COL_数学平均分).NumberFormat = "0.00"
    ws班级.Cells(2, TGT_COL_英语平均分).NumberFormat = "0.00"
    ws班级.Cells(2, TGT_COL_科学平均分).NumberFormat = "0.00"
    ws班级.Cells(2, TGT_COL_道法平均分).NumberFormat = "0.00"

    ' 6. 计算平均分并写入基础数据到目标表
    ReDim arr班级平均分(1 To dict班级数据.count, 1 To 7) ' 班级Key, 语, 数, 英, 科, 道, 总
    classIndex = 0

    For Each 班级key In dict班级数据.Keys
        classIndex = classIndex + 1
        arr班级平均分(classIndex, 1) = CStr(班级key) ' 存储班级Key用于后续回写排名

            With dict班级数据(班级key)
                ws班级.Cells(outputRow, TGT_COL_序号).Value = outputRow - 3  ' 序号
                ws班级.Cells(outputRow, TGT_COL_学校).Value = .Item("学校")
                ws班级.Cells(outputRow, TGT_COL_校点).Value = .Item("校点")
                ws班级.Cells(outputRow, TGT_COL_班级).Value = .Item("班级")
                ws班级.Cells(outputRow, TGT_COL_实考人数).Value = .Item("人数")

                Dim 人数 As Long
                人数 = .Item("人数")
                If 人数 > 0 Then
                    Dim 语平, 数平, 英平, 科平, 道平, 总平 As Double
                    ' 使用各科参考人数计算平均分
                    语平 = IIf(.Item("语文参考人数") > 0, Round(.Item("语文总分") / .Item("语文参考人数"), 2), 0)
                    数平 = IIf(.Item("数学参考人数") > 0, Round(.Item("数学总分") / .Item("数学参考人数"), 2), 0)
                    英平 = IIf(.Item("英语参考人数") > 0, Round(.Item("英语总分") / .Item("英语参考人数"), 2), 0)
                    科平 = IIf(.Item("科学参考人数") > 0, Round(.Item("科学总分") / .Item("科学参考人数"), 2), 0)
                    道平 = IIf(.Item("道法参考人数") > 0, Round(.Item("道法总分") / .Item("道法参考人数"), 2), 0)
                    ' 总分平均分仍然使用总人数计算
                    总平 = Round(.Item("总分（五科）合计") / 人数, 2) ' 这是五科总分的平均分

                    ws班级.Cells(outputRow, TGT_COL_语文平均分).Value = 语平
                    ws班级.Cells(outputRow, TGT_COL_数学平均分).Value = 数平
                    ws班级.Cells(outputRow, TGT_COL_英语平均分).Value = 英平
                    ws班级.Cells(outputRow, TGT_COL_科学平均分).Value = 科平
                    ws班级.Cells(outputRow, TGT_COL_道法平均分).Value = 道平
                    ws班级.Cells(outputRow, TGT_COL_总分平均).Value = 总平

                    arr班级平均分(classIndex, 2) = 语平
                    arr班级平均分(classIndex, 3) = 数平
                    arr班级平均分(classIndex, 4) = 英平
                    arr班级平均分(classIndex, 5) = 科平
                    arr班级平均分(classIndex, 6) = 道平
                    arr班级平均分(classIndex, 7) = 总平
                Else
                    ws班级.Cells(outputRow, TGT_COL_语文平均分).Value = 0
                    ws班级.Cells(outputRow, TGT_COL_数学平均分).Value = 0
                    ws班级.Cells(outputRow, TGT_COL_英语平均分).Value = 0
                    ws班级.Cells(outputRow, TGT_COL_科学平均分).Value = 0
                    ws班级.Cells(outputRow, TGT_COL_道法平均分).Value = 0
                    ws班级.Cells(outputRow, TGT_COL_总分平均).Value = 0

                    arr班级平均分(classIndex, 2) = 0
                    arr班级平均分(classIndex, 3) = 0
                    arr班级平均分(classIndex, 4) = 0
                    arr班级平均分(classIndex, 5) = 0
                    arr班级平均分(classIndex, 6) = 0
                    arr班级平均分(classIndex, 7) = 0
                End If
            ws班级.Cells(outputRow, TGT_COL_语文平均分).NumberFormat = "0.00"
            ws班级.Cells(outputRow, TGT_COL_数学平均分).NumberFormat = "0.00"
            ws班级.Cells(outputRow, TGT_COL_英语平均分).NumberFormat = "0.00"
            ws班级.Cells(outputRow, TGT_COL_科学平均分).NumberFormat = "0.00"
            ws班级.Cells(outputRow, TGT_COL_道法平均分).NumberFormat = "0.00"
            ws班级.Cells(outputRow, TGT_COL_总分平均).NumberFormat = "0.00"


            ' 写入任课教师
            Dim 教师key As String
            教师key = .Item("学校") & "_" & .Item("校点") & "_" & .Item("班级")
            If dict教师.Exists(教师key) Then
                ws班级.Cells(outputRow, TGT_COL_语文任课教师).Value = dict教师(教师key)("语文")
                ws班级.Cells(outputRow, TGT_COL_数学任课教师).Value = dict教师(教师key)("数学")
                ws班级.Cells(outputRow, TGT_COL_英语任课教师).Value = dict教师(教师key)("英语")
                ws班级.Cells(outputRow, TGT_COL_科学任课教师).Value = dict教师(教师key)("科学")
                ws班级.Cells(outputRow, TGT_COL_道法任课教师).Value = dict教师(教师key)("道法")
            Else
                ws班级.Cells(outputRow, TGT_COL_语文任课教师).Value = "N/A"
                ws班级.Cells(outputRow, TGT_COL_数学任课教师).Value = "N/A"
                ws班级.Cells(outputRow, TGT_COL_英语任课教师).Value = "N/A"
                ws班级.Cells(outputRow, TGT_COL_科学任课教师).Value = "N/A"
                ws班级.Cells(outputRow, TGT_COL_道法任课教师).Value = "N/A"
            End If
        End With
        outputRow = outputRow + 1
    Next 班级key

    ' 6. 计算并写入排名
    Call 计算并写入所有排名(ws班级, arr班级平均分, dict班级数据.count)

    ws班级.Columns.AutoFit ' 自动调整列宽

CleanUp:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Set dict班级数据 = Nothing
    Set dict教师 = Nothing
    Set wb = Nothing
    Set ws成绩汇总 = Nothing
    Set ws任课教师 = Nothing
    Set ws班级 = Nothing
    If Err.Number = 0 Then
        MsgBox "统计完成！耗时: " & Format(Timer - startTime, "0.00") & " 秒", vbInformation
    End If
    Exit Sub

ErrorHandler:
    MsgBox "运行时错误 " & Err.Number & ": " & Err.Description, vbCritical, "错误"
    Resume CleanUp
End Sub

Sub 计算并写入所有排名(ws As Worksheet, arrScores() As Variant, classCount As Long)
    ' arrScores: 1=班级Key, 2=语, 3=数, 4=英, 5=科, 6=道, 7=总
    ' 目标表列: 语名次=7, 数名次=10, 英名次=13, 科名次=16, 道名次=19, 总名次=22

    Dim subjectRankCols As Variant
    subjectRankCols = Array(7, 10, 13, 16, 19, 22) ' 对应语文、数学、英语、科学、道法、总分的名次列

    Dim i As Long, j As Long, k As Long
    Dim scoreColIndexInArray As Long
    Dim dict班级数据 As Object
    Set dict班级数据 = CreateObject("Scripting.Dictionary")

    ' 先收集所有班级数据
    For i = 1 To classCount
        dict班级数据.Add i, arrScores(i, 1) ' 存储行号和班级key
    Next i

    For j = 0 To UBound(subjectRankCols) ' 遍历每个需要排名的科目 (语文到总分)
        scoreColIndexInArray = j + 2 ' 分数在 arrScores 数组中的列索引 (从第2列开始)

        ' 创建一个临时数组用于排序: [原始行索引, 分数]
        Dim sortableArray() As Variant
        Dim validCount As Long: validCount = 0
        
        ' 先计算有效记录数(排除育英学校)
        For i = 1 To classCount
            Dim 班级key As String
            班级key = arrScores(i, 1)
            If InStr(班级key, "育英学校") = 0 Then ' 排除育英学校
                validCount = validCount + 1
            End If
        Next i
        
        ReDim sortableArray(1 To validCount, 1 To 2)
        Dim idx As Long: idx = 0
        
        For i = 1 To classCount
            班级key = arrScores(i, 1)
            If InStr(班级key, "育英学校") = 0 Then ' 排除育英学校
                idx = idx + 1
                sortableArray(idx, 1) = i ' 存储原始行号
                sortableArray(idx, 2) = arrScores(i, scoreColIndexInArray) ' 存储分数
            End If
        Next i

        ' 对 sortableArray 按分数降序排序 (使用有效记录数)
        If validCount > 0 Then
            Dim tempVal1 As Variant, tempVal2 As Variant
            For i = 1 To validCount - 1
                For k = i + 1 To validCount
                    On Error Resume Next
                    If CDbl(sortableArray(i, 2)) < CDbl(sortableArray(k, 2)) Then ' 降序
                        tempVal1 = sortableArray(i, 1)
                        tempVal2 = sortableArray(i, 2)
                        sortableArray(i, 1) = sortableArray(k, 1)
                        sortableArray(i, 2) = sortableArray(k, 2)
                        sortableArray(k, 1) = tempVal1
                        sortableArray(k, 2) = tempVal2
                    End If
                    If Err.Number <> 0 Then
                        MsgBox "排序时出错: " & Err.Description, vbExclamation
                        Exit Sub
                    End If
                    On Error GoTo 0
                Next k
            Next i
        End If

        ' 生成排名并写入工作表 (处理并列排名)
        Dim currentRank As Long, actualRank As Long
        If validCount > 0 Then
            currentRank = 1
            actualRank = 1
            ' 写入第一个排名
            ws.Cells(sortableArray(1, 1) + 3, subjectRankCols(j)).Value = actualRank

            ' 从第二个元素开始比较
            For i = 2 To validCount
                currentRank = currentRank + 1
                On Error Resume Next
                ' 安全比较浮点数
                Dim diff As Double
                diff = Abs(CDbl(sortableArray(i, 2)) - CDbl(sortableArray(i - 1, 2)))
                If Err.Number = 0 Then
                    If diff < 0.0001 Then ' 并列
                        ws.Cells(sortableArray(i, 1) + 3, subjectRankCols(j)).Value = actualRank
                    Else
                        actualRank = currentRank
                        ws.Cells(sortableArray(i, 1) + 3, subjectRankCols(j)).Value = actualRank
                    End If
                Else
                    MsgBox "排名比较时出错: " & Err.Description, vbExclamation
                    Exit Sub
                End If
                On Error GoTo 0
            Next i
        End If
    Next j
End Sub
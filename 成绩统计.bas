
Sub 统计班级成绩()
    Dim wb As Workbook
    Dim wsSum As Worksheet  '成绩汇总表
    Dim wsTeacher As Worksheet  '任课教师表
    Dim wsClass As Worksheet  '班级表
    Dim lastRow As Long, i <PERSON>, writeRow As Long
    Dim dict As Object
    Dim classCount As Object
    Dim subjectScores As Object
    Dim rankDict As Object
    
    Set wb = ThisWorkbook
    Set wsSum = wb.Sheets("成绩汇总表")
    Set wsTeacher = wb.Sheets("任课教师")
    Set wsClass = wb.Sheets("班级")
    
    '创建字典对象用于存储数据
    Set dict = CreateObject("Scripting.Dictionary")
    Set classCount = CreateObject("Scripting.Dictionary")
    Set subjectScores = CreateObject("Scripting.Dictionary")
    Set rankDict = CreateObject("Scripting.Dictionary")
    
    '获取数据最后一行
    lastRow = wsSum.Cells(wsSum.Rows.Count, 1).End(xlUp).Row
    
    '读取并统计数据
    For i = 2 To lastRow
        Dim className As String
        className = wsSum.Cells(i, "B").Value & "|" & wsSum.Cells(i, "C").Value & "|" & wsSum.Cells(i, "D").Value & "|" & wsSum.Cells(i, "E").Value
        
        '统计实考人数
        If Not classCount.exists(className) Then
            classCount.Add className, 1
        Else
            classCount(className) = classCount(className) + 1
        End If
        
        '统计各科目总分
        If Not subjectScores.exists(className) Then
            subjectScores.Add className, Array(0, 0, 0, 0, 0, 0) '语文、数学、英语、科学、道法、总分
        End If
        
        Dim scores As Variant
        scores = subjectScores(className)
        scores(0) = scores(0) + wsSum.Cells(i, "I").Value '语文
        scores(1) = scores(1) + wsSum.Cells(i, "J").Value '数学
        scores(2) = scores(2) + wsSum.Cells(i, "K").Value '英语
        scores(3) = scores(3) + wsSum.Cells(i, "L").Value '科学
        scores(4) = scores(4) + wsSum.Cells(i, "M").Value '道法
        scores(5) = scores(5) + wsSum.Cells(i, "V").Value '总分
        subjectScores(className) = scores
        
        '存储基本信息
        If Not dict.exists(className) Then
            dict.Add className, Array(wsSum.Cells(i, "B").Value, wsSum.Cells(i, "C").Value) '学校、校点
        End If
    Next i
    
    '计算平均分并写入班级表
    writeRow = 4 '从第4行开始写入
    Dim classKey As Variant
    Dim serialNum As Long
    serialNum = 1
    
    '获取年级
    Dim grade As String
    grade = wsClass.Range("C1").Value
    
    '创建排名数组
    Dim subjectRanks(5) As Collection '语文、数学、英语、科学、道法、总分的排名数据
    For i = 0 To 5
        Set subjectRanks(i) = New Collection
    Next i
    
    '准备排名数据
    For Each classKey In subjectScores.keys
        If Left(classKey, Len(grade)) = grade Then '只处理指定年级的数据
            Dim avgScores(5) As Double
            For i = 0 To 5
                avgScores(i) = subjectScores(classKey)(i) / classCount(classKey)
                subjectRanks(i).Add Array(classKey, avgScores(i))
            Next i
        End If
    Next classKey
    
    '计算排名
    For i = 0 To 5
        SortCollection subjectRanks(i)
        Set rankDict = CreateObject("Scripting.Dictionary")
        Dim j As Long
        For j = 1 To subjectRanks(i).Count
            rankDict.Add subjectRanks(i).Item(j)(0), j
        Next j
        subjectRanks(i) = rankDict
    Next i
    
    '写入数据
    For Each classKey In subjectScores.keys
        If Left(classKey, Len(grade)) = grade Then '只处理指定年级的数据
            '基本信息
            wsClass.Cells(writeRow, "A").Value = serialNum
            wsClass.Cells(writeRow, "B").Value = dict(classKey)(0) '学校
            wsClass.Cells(writeRow, "C").Value = dict(classKey)(1) '校点
            wsClass.Cells(writeRow, "D").Value = Right(classKey, Len(classKey) - Len(grade)) '班级
            wsClass.Cells(writeRow, "E").Value = classCount(classKey) '实考人数
            
            '计算并写入平均分和排名
            Dim avgScore As Double
            For i = 0 To 4
                avgScore = subjectScores(classKey)(i) / classCount(classKey)
                wsClass.Cells(writeRow, "F" & (i * 3 + 1)).Value = Round(avgScore, 2) '平均分
                wsClass.Cells(writeRow, "G" & (i * 3 + 1)).Value = subjectRanks(i)(classKey) '排名
                
                '查找并写入任课教师
                Dim teacherRow As Range
                Set teacherRow = wsTeacher.Range("A:D").Find(What:=classKey, LookIn:=xlValues, LookAt:=xlWhole)
                If Not teacherRow Is Nothing Then
                    Select Case i
                        Case 0: wsClass.Cells(writeRow, "H").Value = wsTeacher.Cells(teacherRow.Row, "F").Value '语文
                        Case 1: wsClass.Cells(writeRow, "K").Value = wsTeacher.Cells(teacherRow.Row, "G").Value '数学
                        Case 2: wsClass.Cells(writeRow, "N").Value = wsTeacher.Cells(teacherRow.Row, "H").Value '英语
                        Case 3: wsClass.Cells(writeRow, "Q").Value = wsTeacher.Cells(teacherRow.Row, "I").Value '科学
                        Case 4: wsClass.Cells(writeRow, "T").Value = wsTeacher.Cells(teacherRow.Row, "J").Value '道法
                    End Select
                End If
            Next i
            
            '写入总分和总分排名
            wsClass.Cells(writeRow, "U").Value = Round(subjectScores(classKey)(5) / classCount(classKey), 2)
            wsClass.Cells(writeRow, "V").Value = subjectRanks(5)(classKey)
            
            writeRow = writeRow + 1
            serialNum = serialNum + 1
        End If
    Next classKey
End Sub

Sub SortCollection(ByRef col As Collection)
    Dim i As Long, j As Long
    Dim temp As Variant
    
    For i = 1 To col.Count - 1
        For j = i + 1 To col.Count
            If col.Item(j)(1) > col.Item(i)(1) Then
                temp = col.Item(i)
                col.Remove i
                If j = col.Count + 1 Then
                    col.Add temp
                Else
                    col.Add temp, , j
                End If
            End If
        Next j
    Next i
End Sub
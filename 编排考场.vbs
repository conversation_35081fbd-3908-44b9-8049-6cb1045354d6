Sub Main()
    ' 首先检查使用次数和日期
    Dim runCount As Integer
    Dim lastRunDate As String
    Dim currentDate As String
    currentDate = Format(Date, "yyyy-mm-dd")
    
    ' 获取运行计数器和上次运行日期
    runCount = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value
    lastRunDate = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value
    
    ' 检查使用次数或日期是否超过限制
    If runCount >= 5 Or currentDate > "2025-07-20" Then
        MsgBox "程序已达到使用限制，无法继续运行。", vbCritical
        Exit Sub
    End If
    
    ' 增加运行计数器
    runCount = runCount + 1
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value = runCount
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value = currentDate
    
    ' 首先排序数据
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range
    ' 设置源工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表") '假设使用 Sheet1 来存储变数器和日期

'

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
   
    Set sortRange = ws.Range("A1:H" & lastRow)

    Columns("A:H").Select
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("C2:C3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:H" & lastRow)
        .Apply
    End With
    
    ' 在H列生成随机数
    For i = 2 To lastRow
        ws.Cells(i, "H").Value = Rnd()
    Next i
'     执行排序
    Columns("A:H").Select
        '     设置排序范围
    Set sortRange = ws.Range("A1:H" & lastRow)
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("H2:H3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:H" & lastRow)
        .Apply
    End With

    ' 显示用户窗体
    UserForm1.Show
End Sub
Sub ArrangeExaminationRooms(groupA As String, groupB As String, groupC As String, groupD As String, desksPerRoom As Integer)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim gradeLevel As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    Dim currentSchool As String
    Dim previousSchool As String
    Dim groups As Variant
    Dim studentDict As Object
    Dim student As Variant
    Dim studentID As Integer
    Dim groupIndex As Integer
    Dim schoolGroupDict As Object
    Dim schoolGroupKey As Variant
    Dim groupStudents As Variant
    Dim studentInfo As Variant
    Dim currentGroup As String
    Dim pairedStudents As Variant
    Dim pairIndex As Integer
    Dim remainingSeats As Integer ' 记录上一组剩余的座位数
    Dim lastClass As String ' 记录上一个学生的班级
    Dim currentClass As String ' 记录当前学生的班级
    Dim sameClass As Boolean ' 标记是否同班相邻

    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")

    ' 获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row


    ' 初始化变量
    roomNumber = 1
    seatNumber = 1
    previousSchool = ""
    remainingSeats = 0

    ' 将组信息存储在一个数组中
    groups = Array(groupA, groupB, groupC, groupD)

    ' 初始化学生字典（按学校和年级分组）
    Set studentDict = CreateObject("Scripting.Dictionary")

    ' 遍历每一行，为每个学校、每个年级的学生分配编号
    For i = 2 To lastRow
        currentSchool = ws.Cells(i, "A").Value
        gradeLevel = ws.Cells(i, "B").Value

        ' 生成学校和年级的组合键
        Dim dictKey As String
        dictKey = currentSchool & "-" & gradeLevel

        ' 如果字典中不存在该键，则初始化
        If Not studentDict.Exists(dictKey) Then
            Set studentDict(dictKey) = CreateObject("Scripting.Dictionary")
        End If

        studentID = studentDict(dictKey).Count + 1
        ' 为学生分配编号
        studentDict(dictKey).Add studentID, Array(ws.Cells(i, "C").Value, ws.Cells(i, "D").Value, ws.Cells(i, "E").Value, i)
    Next i

    ' 初始化学校和组的字典
    Set schoolGroupDict = CreateObject("Scripting.Dictionary")

    ' 遍历每个学校和年级组，按学校和组进行分组
    For Each schoolGradeKey In studentDict.Keys
        currentSchool = Split(schoolGradeKey, "-")(0)
        gradeLevel = Split(schoolGradeKey, "-")(1)

        ' 确定年级组
        currentGroup = ""
        For groupIndex = 0 To UBound(groups)
            If groups(groupIndex) <> "" And InStr(groups(groupIndex), gradeLevel) > 0 Then
                currentGroup = "组" & (groupIndex + 1) ' 例如：组1, 组2
                Exit For
            End If
        Next groupIndex

        ' 如果未找到年级组，跳过
        If currentGroup = "" Then
            MsgBox "未找到年级组：" & gradeLevel, vbExclamation
            Exit Sub
        End If

        ' 生成学校和组的组合键
        Dim schoolGroupCombinedKey As String
        schoolGroupCombinedKey = currentSchool & "-" & currentGroup

        ' 如果字典中不存在该键，则初始化
        If Not schoolGroupDict.Exists(schoolGroupCombinedKey) Then
            Set schoolGroupDict(schoolGroupCombinedKey) = CreateObject("Scripting.Dictionary")
        End If

        ' 将学生添加到对应的学校和组中
        Set schoolGroupDict(schoolGroupCombinedKey)(gradeLevel) = studentDict(schoolGradeKey)
    Next schoolGradeKey

    ' 遍历每个学校和组，分配考场和座位
    For Each schoolGroupKey In schoolGroupDict.Keys
        currentSchool = Split(schoolGroupKey, "-")(0)
        currentGroup = Split(schoolGroupKey, "-")(1)

        ' 如果遇到不同学校，重置考场号和座位号
        If previousSchool <> currentSchool Then
            roomNumber = 1
            seatNumber = 1
            remainingSeats = 0
        ' 如果上一组有剩余座位，启用下一个考场
        ElseIf remainingSeats > 0 Then
            roomNumber = roomNumber + 1
            seatNumber = 1
            remainingSeats = 0
        End If

        ' 获取当前学校和组的学生
        Set groupStudents = schoolGroupDict(schoolGroupKey)

        ' 配对学生
        pairedStudents = PairStudents(groupStudents)

        ' 遍历配对的学生
        For pairIndex = LBound(pairedStudents, 1) To UBound(pairedStudents, 1)
            ' 分配考场和座位号
            For i = LBound(pairedStudents, 2) To UBound(pairedStudents, 2)
                studentInfo = pairedStudents(pairIndex, i)
                If IsArray(studentInfo) Then
                    currentClass = studentInfo(0) ' 获取当前学生的班级
                    
                    ' 检查是否与前一个学生同班
                    sameClass = False
                    If lastClass <> "" Then
                        If currentClass = lastClass Then
                            sameClass = True
                        End If
                    End If
                    
                    ' 如果同班且不是考场第一个座位，则跳过该座位
                    If sameClass And seatNumber > 1 Then
                        ' 寻找下一个可用座位
                        Dim foundSeat As Boolean
                        foundSeat = False
                        
                        ' 尝试在当前考场寻找非相邻座位
                        For k = seatNumber + 1 To desksPerRoom
                            If ws.Cells(studentInfo(3), "F").Value = "" Then
                                ws.Cells(studentInfo(3), "F").Value = roomNumber
                                ws.Cells(studentInfo(3), "G").Value = k
                                foundSeat = True
                                Exit For
                            End If
                        Next k
                        
                        ' 如果当前考场没有合适座位，则换到下一个考场
                        If Not foundSeat Then
                            roomNumber = roomNumber + 1
                            seatNumber = 1
                            ws.Cells(studentInfo(3), "F").Value = roomNumber
                            ws.Cells(studentInfo(3), "G").Value = seatNumber
                        End If
                    Else
                        ' 正常分配座位
                        ws.Cells(studentInfo(3), "F").Value = roomNumber
                        ws.Cells(studentInfo(3), "G").Value = seatNumber
                    End If
                    
                    lastClass = currentClass ' 更新上一个班级
                End If
            Next i

            ' 增加座位号
            seatNumber = seatNumber + 1

            ' 检查考场是否已满
            If seatNumber > desksPerRoom Then
                roomNumber = roomNumber + 1 ' 增加考场号
                seatNumber = 1 ' 重置座位号
                lastClass = "" ' 换考场后重置上一个班级记录
            End If
        Next pairIndex

        ' 记录上一组剩余的座位数
        remainingSeats = desksPerRoom - seatNumber + 1

        ' 更新上一个学校信息
        previousSchool = currentSchool
    Next schoolGroupKey
    Columns("A:H").Select
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("F2:F3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("G2:G3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:G6000")
        .Apply
    End With
End Sub
Function PairStudents(ByVal groupStudents As Object) As Variant
    Dim gradeLevels As Variant
    Dim numStudents As Long
    Dim maxStudents As Long
    Dim pairedStudents As Variant
    Dim i As Long
    Dim j As Long
    Dim k As Long
    Dim tempStudents As Variant
    Dim classDict As Object
    Dim classGroups As Object
    
    ' 获取所有年级
    gradeLevels = groupStudents.Keys

    ' 找出最大学生数
    maxStudents = 0
    For Each gradeLevel In gradeLevels
        numStudents = groupStudents(gradeLevel).Count
        If numStudents > maxStudents Then
            maxStudents = numStudents
        End If
    Next gradeLevel

    ' 初始化配对数组
    ReDim pairedStudents(1 To maxStudents, 1 To UBound(gradeLevels) + 1)
    
    ' 创建字典存储每个班级的学生
    Set classDict = CreateObject("Scripting.Dictionary")
    
    ' 按班级分组学生
    For Each gradeLevel In gradeLevels
        ' 初始化classGroups字典
        Set classGroups = CreateObject("Scripting.Dictionary")
        
        ' 按学生序号排序
        tempStudents = groupStudents(gradeLevel).Items()
        ' 按学生ID排序（1号、2号...）
        For i = 0 To UBound(tempStudents)
            For j = i + 1 To UBound(tempStudents)
                If tempStudents(i)(3) > tempStudents(j)(3) Then
                    Dim temp As Variant
                    temp = tempStudents(i)
                    tempStudents(i) = tempStudents(j)
                    tempStudents(j) = temp
                End If
            Next j
        Next i
        
        ' 按班级分组
        For i = 0 To UBound(tempStudents)
            Dim className As String
            className = tempStudents(i)(0)
            
            If Not classGroups.Exists(className) Then
                Set classGroups(className) = CreateObject("Scripting.Dictionary")
            End If
            
            classGroups(className).Add classGroups(className).Count + 1, tempStudents(i)
        Next i
        
        ' 验证gradeLevel是否有效
        If Not IsEmpty(gradeLevel) Then
            classDict(gradeLevel) = classGroups
        End If
    Next

    ' 配对学生：确保同班级学生不相邻
    Dim currentClassIndex As Integer
    Dim lastClassIndex As Integer
    
    For i = 1 To maxStudents
        k = 1
        currentClassIndex = (i - 1) Mod UBound(gradeLevels) + 1
        
        For Each gradeLevel In gradeLevels
            If i <= classDict(gradeLevel).Count Then
                ' 获取当前班级的学生
                Dim classKeys As Variant
                classKeys = classDict(gradeLevel).Keys()
                
                ' 确保不同班级的学生交叉排列
                Dim selectedClass As String
                selectedClass = classKeys((currentClassIndex - 1) Mod classDict(gradeLevel).Count)
                
                ' 从班级中按顺序取学生
                Dim studentIndex As Integer
                studentIndex = ((i - 1) \ UBound(gradeLevels)) + 1
                
                If studentIndex <= classDict(gradeLevel)(selectedClass).Count Then
                    pairedStudents(i, k) = classDict(gradeLevel)(selectedClass)(studentIndex)
                Else
                    pairedStudents(i, k) = Empty
                End If
            Else
                pairedStudents(i, k) = Empty
            End If
            
            k = k + 1
        Next gradeLevel
    Next i
    
    PairStudents = pairedStudents
End Function
Function ShuffleArray(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim temp As Variant
    Dim arrayLength As Long

    ' 获取数组的长度
    arrayLength = UBound(arr) - LBound(arr) + 1

    ' 初始化随机数种子
    Randomize

    ' 使用 Fisher-Yates 洗牌算法随机打乱数组
    For i = arrayLength - 1 To 0 Step -1
        ' 随机选择一个索引 j，范围从 0 到 i
        j = Int(Rnd * (i + 1))
        
        ' 交换 arr(i) 和 arr(j)
        temp = arr(LBound(arr) + i)
        arr(LBound(arr) + i) = arr(LBound(arr) + j)
        arr(LBound(arr) + j) = temp
    Next i

    ' 返回打乱后的数组
    ShuffleArray = arr
End Function
import os
import time
import psutil
import win32api
import win32con
import win32gui
import win32process
from datetime import datetime

# 禁止使用聊天软件
CHAT_APPS = ['QQ.exe', 'UC.exe', 'MSN.exe', 'POPO.exe']
def block_chat_apps():
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] in CHAT_APPS:
            proc.kill()

# 禁止运行指定软件
GAME_APPS = ['game1.exe', 'game2.exe']  # 预设三十多种游戏软件
def block_specified_apps():
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] in GAME_APPS:
            proc.kill()

# 禁止使用浏览软件
BROWSER_APPS = ['iexplore.exe', 'Maxthon.exe', 'TT.exe']
def block_browsers():
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] in BROWSER_APPS:
            proc.kill()

# 禁止在设定时间段使用计算机
def block_computer(start_time, end_time):
    now = datetime.now().time()
    if start_time <= now <= end_time:
        os.system('shutdown -s -t 0')

# 禁止在设定时间段上网
def block_network(start_time, end_time):
    now = datetime.now().time()
    if start_time <= now <= end_time:
        os.system('netsh interface set interface "以太网" admin=disable')

# 禁止更改系统时间
def block_time_change():
    win32api.RegSetValueEx(win32api.RegOpenKey(win32con.HKEY_CURRENT_USER, 'Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System', 0, win32con.KEY_SET_VALUE), 'DisableDateChange', 0, win32con.REG_DWORD, 1)

# 禁止使用任务管理器和注册表
def block_taskmgr_regedit():
    win32api.RegSetValueEx(win32api.RegOpenKey(win32con.HKEY_CURRENT_USER, 'Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System', 0, win32con.KEY_SET_VALUE), 'DisableTaskMgr', 0, win32con.REG_DWORD, 1)
    win32api.RegSetValueEx(win32api.RegOpenKey(win32con.HKEY_CURRENT_USER, 'Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\System', 0, win32con.KEY_SET_VALUE), 'DisableRegistryTools', 0, win32con.REG_DWORD, 1)

# 设置自动关机后多长时间不许使用计算机
def block_after_shutdown(block_seconds):
    # 可通过注册表或其他方式记录关机时间，此处简化
    pass

# 磁盘加锁、隐藏和禁止使用USB设备
def block_usb_disk():
    win32api.RegSetValueEx(win32api.RegOpenKey(win32con.HKEY_LOCAL_MACHINE, 'SYSTEM\\CurrentControlSet\\Services\\USBSTOR', 0, win32con.KEY_SET_VALUE), 'Start', 0, win32con.REG_DWORD, 4)
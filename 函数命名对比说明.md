# 函数命名对比说明 - 解决二义性问题

## 问题描述

在原版和重构版的考场编排工具中，存在函数名称冲突的问题，导致二义性。主要冲突的函数有：

1. `CreateCrossGradePairing` - 跨年级配对函数（原版中重复定义）
2. `CreateCrossClassArrangementForSingleGrade` - 单年级交叉班级编排函数

## 解决方案

### 原版文件清理
- **删除重复函数**：原版文件中 `CreateCrossGradePairing` 函数被重复定义了两次，已删除第二个重复定义
- **删除无用函数**：删除了 `CreateCrossClassArrangementForSingleGrade1` 函数
- **修复调用关系**：确保所有函数调用都指向正确的函数定义

## 函数重命名方案

### 1. 跨年级配对函数

#### 原版文件 (`00 小学考场通用编排工具.vbs`)
```vba
Function CreateCrossGradePairing(students As Object) As Variant
```
- **功能**：创建跨年级配对，输出A座、B座标识
- **输出格式**：座位号 + "A" 或 "B"（如：1A、1B、2A、2B）
- **用途**：原版双人座位编排，区分A座和B座

#### 重构版文件 (`考场编排工具重构版.vbs`)
```vba
Function CreateCrossGradeTablePairing(students As Object) As Variant
```
- **功能**：创建跨年级桌号配对，输出相同桌号
- **输出格式**：桌号重复（如：1、1、2、2、3、3）
- **用途**：重构版双人座位编排，同桌共用桌号

### 2. 单年级交叉班级编排函数

#### 原版文件 (`00 小学考场通用编排工具.vbs`)
```vba
Function CreateCrossClassArrangementForSingleGrade(students As Object) As Variant
```
- **功能**：为单个年级创建交叉班级编排
- **用途**：原版中的单年级处理逻辑

#### 重构版文件 (`考场编排工具重构版.vbs`)
```vba
Function CreateCrossClassArrangementForSingleGradeV2(students As Object) As Variant
```
- **功能**：为单个年级创建交叉班级编排（重构版）
- **用途**：重构版中的单年级处理逻辑

## 功能对比表

| 功能 | 原版函数名 | 重构版函数名 | 主要区别 |
|------|------------|--------------|----------|
| 跨年级配对 | `CreateCrossGradePairing` | `CreateCrossGradeTablePairing` | 原版输出A/B座，重构版输出重复桌号 |
| 单年级交叉编排 | `CreateCrossClassArrangementForSingleGrade` | `CreateCrossClassArrangementForSingleGradeV2` | 基本逻辑相同，版本标识区分 |

## 调用关系

### 原版调用关系
```
ArrangeDoubleSeating()
  └── CreateCrossGradePairing()
      └── CreateCrossClassArrangementForSingleGrade()
```

### 重构版调用关系
```
ArrangeDoubleSeating()
  └── CreateCrossGradeTablePairing()
      └── CreateCrossClassArrangementForSingleGradeV2()
```

## 输出差异示例

### 原版输出（A/B座标识）
```
学生1: 考场1, 座位1A
学生2: 考场1, 座位1B
学生3: 考场1, 座位2A
学生4: 考场1, 座位2B
```

### 重构版输出（桌号重复）
```
学生1: 考场1, 桌号1
学生2: 考场1, 桌号1
学生3: 考场1, 桌号2
学生4: 考场1, 桌号2
```

## 使用建议

### 选择原版的情况
- 需要明确区分A座、B座
- 座位管理更精细
- 符合传统座位号概念

### 选择重构版的情况
- 更符合实际考场桌位管理
- 桌号概念更直观
- 便于考场布置和监考

## 兼容性说明

1. **函数名称**：两个版本的函数名称已经区分，不会产生冲突
2. **数据结构**：原版使用"座位号"列，重构版使用"桌号"列
3. **输出格式**：原版输出带A/B标识，重构版输出纯数字桌号
4. **独立运行**：两个版本可以独立运行，互不干扰

## 迁移指南

### 从原版迁移到重构版
1. 将工作表中的"座位号"列标题改为"桌号"
2. 使用重构版的VBA代码
3. 运行测试确保功能正常

### 从重构版迁移到原版
1. 将工作表中的"桌号"列标题改为"座位号"
2. 使用原版的VBA代码
3. 运行测试确保功能正常

## 技术细节

### 函数签名对比
```vba
' 原版
Function CreateCrossGradePairing(students As Object) As Variant
Function CreateCrossClassArrangementForSingleGrade(students As Object) As Variant

' 重构版
Function CreateCrossGradeTablePairing(students As Object) As Variant
Function CreateCrossClassArrangementForSingleGradeV2(students As Object) As Variant
```

### 返回值格式
- **原版**：二维数组，配对后写入时添加A/B后缀
- **重构版**：二维数组，配对后写入相同桌号

## 总结

通过重命名函数解决了二义性问题：
- `CreateCrossGradePairing` → `CreateCrossGradeTablePairing`
- `CreateCrossClassArrangementForSingleGrade` → `CreateCrossClassArrangementForSingleGradeV2`

这样确保了两个版本可以并存，用户可以根据需要选择合适的版本使用。

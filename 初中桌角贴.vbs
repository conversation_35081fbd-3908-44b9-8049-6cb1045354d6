Sub 桌角条打印()
    Dim ws As Worksheet
    Dim wsOutput As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim currentRow As Long
    Dim currentCol As Long
    Dim examRoom As Integer
    Dim seatNumber As Integer
    Dim schoolPoint As String
    Dim prevExamRoom As Integer
    Dim prevSeatNumber As Integer
    Dim prevSchoolPoint As String
    Dim studentInfo As String
    Dim combinedInfo As String
    Dim isFirstRecord As Boolean
    Dim lastExamRoomOfPrevSchoolPoint As Integer

    ' 新增：用于存储每个考场的最后一个座位号
    Dim examRoomLastSeat As Object
    Set examRoomLastSeat = CreateObject("Scripting.Dictionary")

    ' 检查是否存在名为"桌角条打印"的工作表，如果存在则删除
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "桌角条打印" Then
            Application.DisplayAlerts = False
            ws.Delete
            Application.DisplayAlerts = True
            Exit For
        End If
    Next ws

    ' 设置源工作表和输出工作表
    Set ws = ThisWorkbook.Sheets("学生考场编排表")
    Set wsOutput = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.count))
    wsOutput.Name = "桌角条打印"

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.count, 1).End(xlUp).Row

    ' 按学校、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("F2:F" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:G" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 预先分析每个考场的最后一个座位号
    For i = 2 To lastRow
        Dim tempSchool As String
        Dim tempExamRoom As Integer
        Dim tempSeatNumber As Integer

        tempSchool = ws.Cells(i, 1).Value
        tempExamRoom = ws.Cells(i, 6).Value
        tempSeatNumber = ws.Cells(i, 7).Value

        Dim key As String
        key = tempSchool & "_" & tempExamRoom

        ' 记录每个考场的最大座位号
        If examRoomLastSeat.Exists(key) Then
            If tempSeatNumber > examRoomLastSeat(key) Then
                examRoomLastSeat(key) = tempSeatNumber
            End If
        Else
            examRoomLastSeat.Add key, tempSeatNumber
        End If
    Next i

    ' 初始化变量
    currentRow = 1
    currentCol = 1
    prevExamRoom = 0
    prevSeatNumber = 0
    prevSchoolPoint = ""
    studentInfo = ""
    isFirstRecord = True
    lastExamRoomOfPrevSchoolPoint = 0

    ' 遍历源工作表中的每一行
    Dim prevGrade As String
    prevGrade = ""

    For i = 2 To lastRow
        schoolPoint = ws.Cells(i, 1).Value
        Dim currentGrade As String
        currentGrade = ws.Cells(i, 2).Value
        examRoom = ws.Cells(i, 6).Value
        seatNumber = ws.Cells(i, 7).Value

        ' 检查是否是新学校
        If schoolPoint <> prevSchoolPoint Then
            ' 写入上一学校的最后一组信息
            If Not isFirstRecord Then
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                combinedInfo = Trim(combinedInfo)
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo
                Call FormatCell(wsOutput.Cells(currentRow, currentCol), 14, 60)
                studentInfo = ""

                ' 新学校开始前插入分页符
                wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow + 2, 1)
                currentRow = currentRow + 3
            End If

            currentCol = 1

            ' 添加学校标题
            wsOutput.Cells(currentRow, 1).Value = "学校：" & schoolPoint
            With wsOutput.Cells(currentRow, 1).Font
                .Name = "黑体"
                .Size = 18
            End With
            wsOutput.Cells(currentRow, 1).HorizontalAlignment = xlCenter
            currentRow = currentRow + 1

            prevSchoolPoint = schoolPoint
            prevExamRoom = 0
            prevSeatNumber = 0
            prevGrade = ""
            isFirstRecord = True
        End If

        ' 检查考场或座位变化
        If examRoom <> prevExamRoom Or seatNumber <> prevSeatNumber Then
            ' 如果不是第一条记录，写入上一组信息
            If Not isFirstRecord Then
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                combinedInfo = Trim(combinedInfo)
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo
                Call FormatCell(wsOutput.Cells(currentRow, currentCol), 14, 60)

                ' 检查是否是当前考场的最后一个座位
                Dim currentKey As String
                currentKey = schoolPoint & "_" & prevExamRoom

                ' 检查是否是考场变化
                If examRoom <> prevExamRoom Then
                    ' 考场变化时，插入空行并从新页开始
                    currentCol = 1
                    currentRow = currentRow + 3  ' 插入空行分隔考场

                    ' 如果是当前考场的最后一个座位，且下一个是新考场，则插入分页符
                    If examRoomLastSeat.Exists(currentKey) And prevSeatNumber = examRoomLastSeat(currentKey) Then
                        ' 插入分页符，新考场从新页开始
                        wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
                        If wsOutput.Cells(currentRow, 1).Value <> "" Then
                            currentRow = currentRow + 1
                        End If
                    End If
                Else
                    ' 同一考场内，检查是否需要因年级变化而换行
                    Dim needGradeBreak As Boolean
                    needGradeBreak = False

                    ' 如果年级发生变化且在同一考场内，需要插入空行
                    If prevGrade <> "" And currentGrade <> prevGrade Then
                        needGradeBreak = True
                    End If

                    If needGradeBreak Then
                        ' 年级变化时，强制换行并插入空行
                        currentCol = 1
                        currentRow = currentRow + 3  ' 多空一行来分隔年级
                    Else
                        ' 正常移动到下一个单元格
                        currentCol = currentCol + 2
                        If currentCol > 5 Then
                            currentCol = 1
                            currentRow = currentRow + 2
                        End If
                    End If
                End If

                studentInfo = ""
            End If

            ' 座位号为1时，从第一列开始新行（但不是新考场的情况）
            If seatNumber = 1 And prevSeatNumber > 0 And examRoom = prevExamRoom Then
                currentCol = 1
                currentRow = currentRow + 2
            End If

            ' 更新上一组信息
            prevExamRoom = examRoom
            prevSeatNumber = seatNumber
            isFirstRecord = False
        End If

        ' 合并学生信息
        studentInfo = studentInfo & ws.Cells(i, 2).Value & "年级 " & ws.Cells(i, 3).Value & "班" & vbCrLf & ws.Cells(i, 4).Value & " " & ws.Cells(i, 5).Value & vbCrLf

        ' 更新上一个年级
        prevGrade = currentGrade
    Next i

    ' 写入最后一组信息
    If Not isFirstRecord Then
        combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
        combinedInfo = Trim(combinedInfo)
        wsOutput.Cells(currentRow, currentCol).Value = combinedInfo
        Call FormatCell(wsOutput.Cells(currentRow, currentCol), 14, 65)
    End If

    ' 设置列宽
    wsOutput.Columns("B").ColumnWidth = 7
    wsOutput.Columns("D").ColumnWidth = 7

    ' 设置整个工作表的打印区域
    wsOutput.PageSetup.PrintArea = wsOutput.UsedRange.Address

    ' 打印设置
    With wsOutput.PageSetup
        .Orientation = xlPortrait
        .PaperSize = xlPaperA4
        .TopMargin = Application.CentimetersToPoints(1)
        .BottomMargin = Application.CentimetersToPoints(1)
        .LeftMargin = Application.CentimetersToPoints(0.5)
        .RightMargin = Application.CentimetersToPoints(0.5)
        .FitToPagesWide = 1
        .FitToPagesTall = False
        .LeftFooter = "&P 页，共 &N 页"
        .RightFooter = "&P 页，共 &N 页"
    End With

    ' 打印预览
    wsOutput.PrintPreview

    ' 按考场分开打印功能
    Call PrintByExamRoom(ws, wsOutput, lastRow)
'        For Each sheet In ThisWorkbook.Sheets
'            If sheet.Name = "桌角条打印" Then
'                Application.DisplayAlerts = False
'                sheet.Delete
'                Application.DisplayAlerts = True
'            End If
'        Next sheet
End Sub

' 格式化单元格的辅助函数
Private Sub FormatCell(cell As Range, fontSize As Integer, rowHeight As Double)
    With cell.Font
        .Name = "黑体"
        .Size = fontSize
    End With
    cell.HorizontalAlignment = xlCenter

    With cell.Borders
        .LineStyle = xlDouble
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With

    cell.rowHeight = rowHeight
    cell.ColumnWidth = 28
End Sub

' 按考场分开打印的辅助函数
Private Sub PrintByExamRoom(ws As Worksheet, wsOutput As Worksheet, lastRow As Long)
    Dim uniqueExamRooms As Object
    Set uniqueExamRooms = CreateObject("Scripting.Dictionary")

    ' 收集所有考场号
    Dim i As Long
    For i = 2 To lastRow
        Dim examRoom As Integer
        examRoom = ws.Cells(i, 6).Value  ' 考场号在第6列
        If Not uniqueExamRooms.Exists(examRoom) Then
            uniqueExamRooms.Add examRoom, 1
        End If
    Next i

    ' 为每个考场设置打印区域
    Dim currentExamRoom As Variant
    For Each currentExamRoom In uniqueExamRooms.Keys
        Dim printRange As Range
        Dim cell As Range
        Set printRange = Nothing

        For Each cell In wsOutput.UsedRange
            If InStr(cell.Value, "第" & currentExamRoom & "考场") > 0 Then
                If printRange Is Nothing Then
                    Set printRange = cell.Resize(1, 5)
                Else
                    Set printRange = Union(printRange, cell.Resize(1, 5))
                End If
            End If
        Next cell

        If Not printRange Is Nothing Then
            wsOutput.PageSetup.PrintArea = printRange.Address

            With wsOutput.PageSetup
                .Orientation = xlPortrait
                .PaperSize = xlPaperA4
                .TopMargin = Application.CentimetersToPoints(1)
                .BottomMargin = Application.CentimetersToPoints(1)
                .LeftMargin = Application.CentimetersToPoints(1.8)
                .RightMargin = Application.CentimetersToPoints(1.8)
                .FitToPagesWide = 1
                .FitToPagesTall = False
            End With

        End If
    Next currentExamRoom
End Sub
# 考场编排通用工具使用说明

## 功能概述

本工具实现了WPS中的考场编排功能，支持单人座位和双人座位两种模式，能够自动为学生分配考场号和座位号。

## 数据结构要求

### 工作表名称
- 默认工作表名称：`考场编排`
- 可在代码中修改 `g_WorksheetName` 变量来更改工作表名称

### 数据列结构
工作表应包含以下列（按顺序）：

| 列号 | 列名 | 说明 |
|------|------|------|
| A | 学校 | 学校名称 |
| B | 校点 | 校点名称 |
| C | 年级 | 年级信息（如：三年级、四年级） |
| D | 班级 | 班级信息 |
| E | 准考号 | 学生准考号 |
| F | 姓名 | 学生姓名 |
| G | 考场号 | 程序自动填写 |
| H | 座位号 | 程序自动填写 |

## 使用步骤

### 1. 准备数据
- 确保工作表名称为"考场编排"
- 按照上述列结构整理学生数据
- 第一行应为列标题

### 2. 运行程序
- 在VBA编辑器中运行 `Main()` 子程序
- 或者在WPS中按 Alt+F8 选择 Main 运行

### 3. 设置参数

#### 考场容量设置
- 输入每个考场的座位数（如：30）
- 必须为正整数

#### 座位模式选择
- **单人座位**：每个座位坐一个学生
- **双人座位**：每个座位坐两个学生（需要设置年级组合）

#### 年级组合设置（仅双人模式）
- 最多可设置4个年级组合
- 每个组合可包含多个年级（用逗号分隔）
- 示例：
  - 组合1：三年级,四年级
  - 组合2：五年级,六年级
- 同一组合内的不同年级学生可以坐在同一桌

## 编排规则

### 基本规则
1. **独立编排**：各学校各校点独立建考场序号
2. **连续编号**：校点内各考场连续编号
3. **重置机制**：学校、校点不同时，重置考场号和座位号为1
4. **唯一标识**：学校+校点+年级+班级组合作为唯一标识

### 单人座位模式
- 按学校+校点+年级分组
- 每个学生占用一个座位
- 座位号从1开始连续编号
- **交叉编排**：同校、同年级、不同班学生交叉编排（除非只有一个班）

### 双人座位模式
- 按学校+校点+年级组合分组
- **跨年级配对**：确保同桌的两个学生来自不同年级
- 每桌两个学生共用相同的座位号，分为A座和B座
- **座位标识**：1A、1B表示1号桌的A座和B座

### 交叉编排详细说明

#### 交叉编排原理
交叉编排确保同一学校、同一年级的不同班级学生能够交替排列，避免同班学生集中在一起。

#### 编排算法
1. **多班级情况**：
   - 按轮次从各班级依次取学生
   - 例如：1班第1个 → 2班第1个 → 3班第1个 → 1班第2个 → 2班第2个 → 3班第2个...
   - 结果：不同班级学生交替出现

2. **单班级情况**：
   - 如果某年级只有一个班级，则按原顺序排列
   - 不进行交叉编排

#### 编排示例
假设三年级有3个班级，每班3人：
- **编排前**：1班(张三,李四,王五), 2班(赵六,钱七,孙八), 3班(周九,吴十,郑一)
- **编排后**：张三(1班) → 赵六(2班) → 周九(3班) → 李四(1班) → 钱七(2班) → 吴十(3班) → 王五(1班) → 孙八(2班) → 郑一(3班)

#### 双人座位跨年级配对
在双人座位模式下：
1. **跨年级配对**：同桌的两个学生必须来自不同年级
2. **配对规则**：三年级学生与四年级学生配对，五年级学生与六年级学生配对
3. **座位标识**：每桌有相同座位号，但分为A座和B座
4. **单年级处理**：如果年级组合中只有一个年级，则按班级交叉编排后配对

#### 跨年级配对示例
假设三、四年级组合，每年级2人：
- **配对前**：三年级(张三,李四), 四年级(王五,赵六)
- **配对后**：1号桌(张三-1A,王五-1B), 2号桌(李四-2A,赵六-2B)
- **结果**：每桌都是三年级+四年级的组合

## 性能优化

### 数组和字典优化
- 使用数组读取数据，提高读取速度
- 使用字典进行分组，提高查找效率
- 关闭屏幕更新和自动计算，提升运行速度

### 内存管理
- 程序运行完成后自动恢复Excel设置
- 使用错误处理机制确保程序稳定性

## 注意事项

### 数据要求
1. 学校、校点、年级、班级列不能为空
2. 准考号和姓名建议填写完整
3. 数据应从第2行开始（第1行为标题）

### 年级组合设置
1. 双人模式下至少需要设置一个年级组合
2. 年级名称应与数据中的年级列完全匹配
3. 可以使用中文数字（如：三年级）或阿拉伯数字（如：3年级）

### 错误处理
- 如果工作表不存在，程序会提示错误
- 如果数据为空，程序会提示并退出
- 运行过程中如有错误，会显示错误信息

## 输出结果

程序运行完成后：
- G列（考场号）：显示学生所在考场编号
- H列（座位号）：显示学生的座位编号
- 考场号按学校+校点独立编号
- 座位号在每个考场内从1开始编号

## 技术特点

1. **模块化设计**：功能分离，便于维护
2. **错误处理**：完善的错误处理机制
3. **性能优化**：使用数组和字典提高效率
4. **用户友好**：简单的对话框界面
5. **灵活配置**：支持多种座位模式和年级组合

## 扩展功能

如需扩展功能，可以修改以下部分：
- 修改 `g_WorksheetName` 变量更改工作表名称
- 修改列索引适应不同的数据结构
- 添加更多的年级组合验证规则
- 增加座位排列的特殊要求

## 使用示例

### 示例1：单人座位编排
```vba
' 直接调用主程序
Sub RunSingleSeating()
    Call Main()
    ' 在对话框中选择：
    ' 1. 考场容量：30
    ' 2. 座位模式：选择"否"（单人座位）
End Sub
```

### 示例2：双人座位编排（跨年级配对）
```vba
' 直接调用主程序
Sub RunDoubleSeating()
    Call Main()
    ' 在对话框中选择：
    ' 1. 考场容量：30
    ' 2. 座位模式：选择"是"（双人座位）
    ' 3. 年级组合设置：
    '    - 组合1：三年级,四年级
    '    - 组合2：五年级,六年级
    '    - 组合3：（留空）
    '    - 组合4：（留空）
    ' 结果：同桌学生来自不同年级，座位号为1A、1B、2A、2B...
End Sub
```

### 示例3：测试交叉编排功能
```vba
' 测试交叉编排功能
Sub TestCrossArrangement()
    ' 1. 创建测试数据
    Call CreateTestData()

    ' 2. 测试交叉编排
    Call TestCrossClassArrangement()

    ' 3. 查看编排结果分析
    ' 程序会自动显示班级分布分析
End Sub
```

### 示例4：测试跨年级配对功能
```vba
' 测试跨年级配对功能
Sub TestCrossGradePairing()
    ' 1. 创建测试数据
    Call CreateTestData()

    ' 2. 测试双人座位编排
    Call TestDoubleSeating()

    ' 3. 查看跨年级配对分析
    ' 程序会自动显示配对结果分析
End Sub
```

### 示例5：完整测试流程
```vba
' 运行完整测试流程
Sub RunCompleteTest()
    ' 1. 创建测试数据
    Call CreateTestData()

    ' 2. 测试交叉编排
    Call TestCrossClassArrangement()

    ' 3. 清空结果
    Call ClearArrangement()

    ' 4. 测试跨年级配对
    Call TestDoubleSeating()
End Sub
```

## 常见问题解答

### Q1: 程序提示"未找到'考场编排'工作表"怎么办？
**A1:** 请确保：
- 工作表名称完全匹配（区分大小写）
- 工作表确实存在于当前工作簿中
- 可以修改代码中的 `g_WorksheetName` 变量来适应不同的工作表名称

### Q2: 双人座位模式下，如何确保不同年级的学生坐在一起？
**A2:** 在年级组合设置中，将需要混合的年级写在同一个组合中，用逗号分隔。例如：
- 组合1：三年级,四年级
- 组合2：五年级,六年级

### Q3: 考场编排的结果可以重新运行吗？
**A3:** 可以。程序每次运行时会自动清空G列和H列的内容，然后重新编排。

### Q4: 如何处理学生人数不能被考场容量整除的情况？
**A4:** 程序会自动处理：
- 最后一个考场可能不满员
- 双人座位模式下，奇数学生会有一个空位

### Q5: 程序运行很慢怎么办？
**A5:** 程序已经进行了性能优化：
- 使用数组读取数据
- 使用字典进行分组
- 关闭屏幕更新
- 如果数据量很大，可以考虑分批处理

### Q6: 交叉编排是如何工作的？
**A6:** 交叉编排的工作原理：
- 同一学校、同一年级的不同班级学生会交替排列
- 例如：1班学生 → 2班学生 → 3班学生 → 1班学生...
- 如果某年级只有一个班级，则不进行交叉编排
- 可以运行 `TestCrossClassArrangement()` 查看效果

### Q7: 如何验证交叉编排是否成功？
**A7:** 验证方法：
- 运行 `TestCrossClassArrangement()` 函数
- 程序会自动显示班级分布分析
- 查看考场中班级序列，应该看到不同班级交替出现
- 例如："1班-2班-3班-1班-2班-3班"表示交叉编排成功

### Q8: 双人座位模式下跨年级配对如何工作？
**A8:** 双人座位模式的跨年级配对：
- 确保同桌的两个学生来自不同年级
- 例如：三年级学生与四年级学生配对
- 座位号格式：1A、1B表示1号桌的A座和B座
- 如果只有一个年级，则按班级交叉编排后配对

### Q9: 如何验证跨年级配对是否成功？
**A9:** 验证方法：
- 运行 `TestDoubleSeating()` 函数
- 程序会自动显示跨年级配对分析
- 查看分析结果中的桌号配对信息
- 例如："[1号桌:三年级+四年级]"表示配对成功

### Q10: A座和B座有什么区别？
**A10:** A座和B座的说明：
- A座和B座共用相同的桌号
- A座通常分配给年级组合中的第一个年级
- B座通常分配给年级组合中的第二个年级
- 例如：三、四年级组合中，A座给三年级，B座给四年级

## 版本更新记录

### v1.2 (2024)
- **重大更新**：双人座位模式改为跨年级配对
- **新增功能**：A座、B座标识系统
- **配对规则**：确保同桌学生来自不同年级
- **测试增强**：添加跨年级配对分析功能
- **文档更新**：详细说明跨年级配对原理

### v1.1 (2024)
- **新增功能**：同校、同年级、不同班交叉编排
- **优化算法**：改进配对逻辑，确保班级交替排列
- **增强测试**：添加交叉编排测试和结果分析功能
- **完善文档**：详细说明交叉编排原理和使用方法

### v1.0 (2024)
- 初始版本
- 支持单人和双人座位编排
- 支持多年级组合
- 优化性能和错误处理

## 联系支持

如有问题或需要定制功能，请联系开发人员。

## 附录：完整代码文件列表

1. **00 小学考场通用编排工具.vbs** - 主程序文件
2. **测试示例.vbs** - 测试和示例代码
3. **考场编排工具使用说明.md** - 本说明文档

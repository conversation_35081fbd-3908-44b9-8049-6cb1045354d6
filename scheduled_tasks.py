import os
import time
from datetime import datetime, timedelta
import sched

# 初始化调度器
scheduler = sched.scheduler(time.time, time.sleep)

# 定义任务函数
TASKS = {
    'remind': lambda message: print(message),
    'open_file': lambda file_path: os.startfile(file_path),
    'execute_program': lambda program_path: os.system(program_path),
    'open_url': lambda url: os.system(f'start {url}'),
    'shutdown': lambda: os.system('shutdown -s -t 0'),
    'lock': lambda: os.system('rundll32.exe user32.dll,LockWorkStation'),
    'disconnect_network': lambda: os.system('netsh interface set interface \"以太网\
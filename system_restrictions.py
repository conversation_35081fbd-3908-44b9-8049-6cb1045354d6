"""
System restriction functionality.
"""
import os
import time
import logging
import threading
import datetime
import win32api
import win32con
import psutil
from datetime import datetime, time as dt_time

import utils
from config import Config, CHAT_APPS, GAME_APPS, BROWSER_APPS

logger = logging.getLogger("定时关机")

class SystemRestrictions:
    """System restrictions controller."""
    
    def __init__(self, config):
        """Initialize the system restrictions controller.
        
        Args:
            config: The application configuration.
        """
        self.config = config
        self.restriction_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Custom app lists
        self.custom_blocked_apps = []
    
    def start_restrictions(self):
        """Start enforcing system restrictions."""
        if self.is_running:
            return False
        
        self.stop_event.clear()
        self.restriction_thread = threading.Thread(target=self._restriction_thread, daemon=True)
        self.restriction_thread.start()
        self.is_running = True
        
        logger.info("Started system restrictions")
        return True
    
    def stop_restrictions(self):
        """Stop enforcing system restrictions."""
        if not self.is_running:
            return False
        
        self.stop_event.set()
        if self.restriction_thread:
            self.restriction_thread.join(timeout=1.0)
        self.is_running = False
        
        logger.info("Stopped system restrictions")
        return True
    
    def add_custom_blocked_app(self, app_name):
        """Add a custom application to the block list."""
        if app_name not in self.custom_blocked_apps:
            self.custom_blocked_apps.append(app_name)
            logger.info(f"Added {app_name} to custom blocked apps")
            return True
        return False
    
    def remove_custom_blocked_app(self, app_name):
        """Remove a custom application from the block list."""
        if app_name in self.custom_blocked_apps:
            self.custom_blocked_apps.remove(app_name)
            logger.info(f"Removed {app_name} from custom blocked apps")
            return True
        return False
    
    def _restriction_thread(self):
        """Thread function to enforce system restrictions."""
        while not self.stop_event.is_set():
            try:
                # Check and enforce all enabled restrictions
                if self.config.get("block_chat"):
                    self._block_chat_apps()
                
                if self.config.get("block_games"):
                    self._block_game_apps()
                
                if self.config.get("block_browsers"):
                    self._block_browser_apps()
                
                if self.config.get("block_computer_time"):
                    self._block_computer_time_usage()
                
                if self.config.get("block_network_time"):
                    self._block_network_time_usage()
                
                if self.config.get("block_time_change"):
                    self._block_time_change()
                
                if self.config.get("block_task_manager") or self.config.get("block_registry"):
                    self._block_system_tools()
                
                if self.config.get("block_usb"):
                    self._block_usb_devices()
                
                # Block custom apps
                self._block_custom_apps()
                
                # Sleep for a short time before checking again
                time.sleep(2.0)
            except Exception as e:
                logger.error(f"Error enforcing system restrictions: {e}")
                time.sleep(5.0)  # Wait a bit longer on error
    
    def _block_chat_apps(self):
        """Block chat applications."""
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] in CHAT_APPS:
                    logger.info(f"Terminating chat app: {proc.info['name']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.debug(f"Error terminating process: {e}")
    
    def _block_game_apps(self):
        """Block game applications."""
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] in GAME_APPS:
                    logger.info(f"Terminating game app: {proc.info['name']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.debug(f"Error terminating process: {e}")
    
    def _block_browser_apps(self):
        """Block browser applications."""
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] in BROWSER_APPS:
                    logger.info(f"Terminating browser app: {proc.info['name']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.debug(f"Error terminating process: {e}")
    
    def _block_custom_apps(self):
        """Block custom applications."""
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] in self.custom_blocked_apps:
                    logger.info(f"Terminating custom blocked app: {proc.info['name']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.debug(f"Error terminating process: {e}")
    
    def _block_computer_time_usage(self):
        """Block computer usage during specified time period."""
        start_time_str = self.config.get("block_computer_start")
        end_time_str = self.config.get("block_computer_end")
        
        start_time = utils.parse_time(start_time_str)
        end_time = utils.parse_time(end_time_str)
        
        if start_time is None or end_time is None:
            logger.error(f"Invalid block computer time: {start_time_str} - {end_time_str}")
            return
        
        now = datetime.now().time()
        
        # Check if current time is within the blocked period
        if self._is_time_in_range(now, start_time, end_time):
            logger.info(f"Computer usage blocked during {start_time_str} - {end_time_str}, shutting down")
            os.system("shutdown /s /t 60 /c \"定时关机: 当前时间段禁止使用计算机，系统将在60秒后关闭\"")
    
    def _block_network_time_usage(self):
        """Block network usage during specified time period."""
        start_time_str = self.config.get("block_network_start")
        end_time_str = self.config.get("block_network_end")
        
        start_time = utils.parse_time(start_time_str)
        end_time = utils.parse_time(end_time_str)
        
        if start_time is None or end_time is None:
            logger.error(f"Invalid block network time: {start_time_str} - {end_time_str}")
            return
        
        now = datetime.now().time()
        
        # Check if current time is within the blocked period
        if self._is_time_in_range(now, start_time, end_time):
            logger.info(f"Network usage blocked during {start_time_str} - {end_time_str}, disabling network")
            os.system('netsh interface set interface "以太网" admin=disable')
            os.system('netsh interface set interface "WLAN" admin=disable')
        else:
            # Re-enable network interfaces if they were disabled
            os.system('netsh interface set interface "以太网" admin=enable')
            os.system('netsh interface set interface "WLAN" admin=enable')
    
    def _block_time_change(self):
        """Block system time changes."""
        try:
            key = win32api.RegOpenKey(
                win32con.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Policies\System',
                0,
                win32con.KEY_SET_VALUE
            )
            win32api.RegSetValueEx(key, 'DisableChangeTime', 0, win32con.REG_DWORD, 1)
            win32api.RegCloseKey(key)
        except Exception as e:
            logger.error(f"Error blocking time change: {e}")
    
    def _block_system_tools(self):
        """Block task manager and registry editor."""
        try:
            key = win32api.RegOpenKey(
                win32con.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Policies\System',
                0,
                win32con.KEY_SET_VALUE
            )
            
            if self.config.get("block_task_manager"):
                win32api.RegSetValueEx(key, 'DisableTaskMgr', 0, win32con.REG_DWORD, 1)
            
            if self.config.get("block_registry"):
                win32api.RegSetValueEx(key, 'DisableRegistryTools', 0, win32con.REG_DWORD, 1)
            
            win32api.RegCloseKey(key)
        except Exception as e:
            logger.error(f"Error blocking system tools: {e}")
    
    def _block_usb_devices(self):
        """Block USB storage devices."""
        try:
            key = win32api.RegOpenKey(
                win32con.HKEY_LOCAL_MACHINE,
                r'SYSTEM\CurrentControlSet\Services\USBSTOR',
                0,
                win32con.KEY_SET_VALUE
            )
            win32api.RegSetValueEx(key, 'Start', 0, win32con.REG_DWORD, 4)
            win32api.RegCloseKey(key)
        except Exception as e:
            logger.error(f"Error blocking USB devices: {e}")
    
    def _is_time_in_range(self, check_time, start_time, end_time):
        """Check if a time is within a range.
        
        Args:
            check_time: The time to check.
            start_time: The start of the range.
            end_time: The end of the range.
            
        Returns:
            True if check_time is within the range, False otherwise.
        """
        if start_time <= end_time:
            return start_time <= check_time <= end_time
        else:
            # Handle ranges that cross midnight
            return check_time >= start_time or check_time <= end_time
    
    def unblock_all(self):
        """Remove all system restrictions."""
        try:
            # Unblock task manager and registry editor
            key = win32api.RegOpenKey(
                win32con.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Policies\System',
                0,
                win32con.KEY_SET_VALUE
            )
            win32api.RegSetValueEx(key, 'DisableTaskMgr', 0, win32con.REG_DWORD, 0)
            win32api.RegSetValueEx(key, 'DisableRegistryTools', 0, win32con.REG_DWORD, 0)
            win32api.RegSetValueEx(key, 'DisableChangeTime', 0, win32con.REG_DWORD, 0)
            win32api.RegCloseKey(key)
            
            # Unblock USB devices
            key = win32api.RegOpenKey(
                win32con.HKEY_LOCAL_MACHINE,
                r'SYSTEM\CurrentControlSet\Services\USBSTOR',
                0,
                win32con.KEY_SET_VALUE
            )
            win32api.RegSetValueEx(key, 'Start', 0, win32con.REG_DWORD, 3)
            win32api.RegCloseKey(key)
            
            # Enable network interfaces
            os.system('netsh interface set interface "以太网" admin=enable')
            os.system('netsh interface set interface "WLAN" admin=enable')
            
            logger.info("Removed all system restrictions")
            return True
        except Exception as e:
            logger.error(f"Error removing system restrictions: {e}")
            return False

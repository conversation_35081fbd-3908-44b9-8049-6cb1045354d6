# 考场编排工具 - 快速开始指南

## 5分钟快速上手

### 第一步：准备数据
1. 在WPS表格中创建名为"考场编排"的工作表
2. 设置以下列标题（第1行）：
   ```
   A列：学校    B列：校点    C列：年级    D列：班级
   E列：准考号  F列：姓名    G列：考场号  H列：座位号
   ```
3. 从第2行开始输入学生数据

### 第二步：导入代码
1. 按 `Alt + F11` 打开VBA编辑器
2. 右键点击工程 → 插入 → 模块
3. 将 `00 小学考场通用编排工具.vbs` 中的代码复制粘贴到模块中

### 第三步：运行程序
1. 按 `Alt + F8` 或在VBA编辑器中按 `F5`
2. 选择 `Main` 程序运行
3. 按照对话框提示设置参数

### 第四步：查看结果
- G列显示考场号
- H列显示座位号
- 各学校各校点的考场号独立编排

## 测试功能

### 快速测试
1. 运行 `CreateTestData()` 创建测试数据
2. 运行 `TestCrossClassArrangement()` 测试交叉编排功能
3. 查看编排结果和分析报告
4. 运行 `Main()` 进行正式编排

### 参数示例

#### 单人座位设置
- 考场容量：30
- 座位模式：选择"否"

#### 双人座位设置（跨年级配对）
- 考场容量：30
- 座位模式：选择"是"
- 年级组合1：三年级,四年级
- 年级组合2：五年级,六年级
- 结果：同桌学生来自不同年级，座位号为1A、1B、2A、2B...

## 常用操作

### 重新编排
直接再次运行 `Main()` 即可，程序会自动清空之前的结果

### 清空结果
运行 `ClearArrangement()` 清空G列和H列

### 修改工作表名称
在代码中修改：
```vba
g_WorksheetName = "你的工作表名称"
```

## 新功能：交叉编排

### 什么是交叉编排？
- 同一学校、同一年级的不同班级学生交替排列
- 避免同班学生集中在一起
- 例如：1班→2班→3班→1班→2班→3班...

### 交叉编排的优势
- 更公平的考试环境
- 减少同班学生之间的干扰
- 自动处理单班级情况（不进行交叉）

## 新功能：跨年级配对

### 什么是跨年级配对？
- 双人座位模式下，同桌的两个学生来自不同年级
- 例如：三年级学生与四年级学生坐同一桌
- 座位号格式：1A、1B表示1号桌的A座和B座

### 跨年级配对的优势
- 避免同年级学生相互影响
- 更好的考试纪律管理
- 符合混合年级考试要求

### 如何验证交叉编排？
运行 `TestCrossClassArrangement()` 查看班级分布分析

### 如何验证跨年级配对？
运行 `TestDoubleSeating()` 查看跨年级配对分析

## 注意事项

1. **数据完整性**：确保学校、校点、年级、班级列不为空
2. **工作表名称**：默认为"考场编排"，可在代码中修改
3. **年级格式**：支持"三年级"、"3年级"等格式
4. **备份数据**：建议在编排前备份原始数据
5. **交叉编排**：多班级时自动启用，单班级时自动跳过

## 问题排查

### 常见错误
- "未找到工作表"：检查工作表名称是否正确
- "没有数据"：确保从第2行开始有学生数据
- "年级组合错误"：检查年级名称是否与数据匹配

### 获取帮助
查看完整的 `考场编排工具使用说明.md` 文档获取详细信息。

---

**提示**：首次使用建议先运行测试数据熟悉功能，然后再使用实际数据。

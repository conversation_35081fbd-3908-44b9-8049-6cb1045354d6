' ========================================
' 考场编排工具测试示例
' 用于测试考场编排功能
' ========================================

Sub CreateTestData()
    ' 创建测试数据工作表
    Dim ws As Worksheet
    Dim wsName As String
    wsName = "考场编排"

    ' 检查工作表是否存在，如果存在则删除
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(wsName)
    If Not ws Is Nothing Then
        Application.DisplayAlerts = False
        ws.Delete
        Application.DisplayAlerts = True
    End If
    On Error GoTo 0

    ' 创建新的工作表
    Set ws = ThisWorkbook.Worksheets.Add
    ws.Name = wsName

    ' 设置列标题
    ws.Cells(1, 1).Value = "学校"
    ws.Cells(1, 2).Value = "校点"
    ws.Cells(1, 3).Value = "年级"
    ws.Cells(1, 4).Value = "班级"
    ws.Cells(1, 5).Value = "准考号"
    ws.Cells(1, 6).Value = "姓名"
    ws.Cells(1, 7).Value = "考场号"
    ws.Cells(1, 8).Value = "座位号"

    ' 添加测试数据
    Dim row As Integer
    row = 2

    ' 思茅一小数据 - 测试交叉编排
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "1班", "001001", "张三")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "1班", "001002", "李四")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "1班", "001003", "王五")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "2班", "001004", "赵六")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "2班", "001005", "钱七")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "2班", "001006", "孙八")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "3班", "001007", "周九")
    Call AddStudentData(ws, row, "思茅一小", "本部", "三年级", "3班", "001008", "吴十")

    Call AddStudentData(ws, row, "思茅一小", "本部", "四年级", "1班", "001009", "郑一")
    Call AddStudentData(ws, row, "思茅一小", "本部", "四年级", "1班", "001010", "王二")
    Call AddStudentData(ws, row, "思茅一小", "本部", "四年级", "2班", "001011", "李三")
    Call AddStudentData(ws, row, "思茅一小", "本部", "四年级", "2班", "001012", "张四")

    ' 思茅二小数据 - 测试单班情况
    Call AddStudentData(ws, row, "思茅二小", "本部", "三年级", "1班", "002001", "陈五")
    Call AddStudentData(ws, row, "思茅二小", "本部", "三年级", "1班", "002002", "林六")
    Call AddStudentData(ws, row, "思茅二小", "本部", "三年级", "1班", "002003", "黄七")
    Call AddStudentData(ws, row, "思茅二小", "本部", "三年级", "1班", "002004", "刘八")

    Call AddStudentData(ws, row, "思茅二小", "本部", "四年级", "1班", "002005", "杨九")
    Call AddStudentData(ws, row, "思茅二小", "本部", "四年级", "1班", "002006", "何十")

    ' 思茅三小分校数据 - 测试多班交叉
    Call AddStudentData(ws, row, "思茅三小", "分校", "五年级", "1班", "003001", "马一")
    Call AddStudentData(ws, row, "思茅三小", "分校", "五年级", "1班", "003002", "朱二")
    Call AddStudentData(ws, row, "思茅三小", "分校", "五年级", "2班", "003003", "许三")
    Call AddStudentData(ws, row, "思茅三小", "分校", "五年级", "2班", "003004", "邓四")
    Call AddStudentData(ws, row, "思茅三小", "分校", "五年级", "3班", "003005", "曾五")

    Call AddStudentData(ws, row, "思茅三小", "分校", "六年级", "1班", "003006", "彭六")
    Call AddStudentData(ws, row, "思茅三小", "分校", "六年级", "1班", "003007", "苏七")
    Call AddStudentData(ws, row, "思茅三小", "分校", "六年级", "2班", "003008", "卢八")
    Call AddStudentData(ws, row, "思茅三小", "分校", "六年级", "2班", "003009", "蒋九")

    ' 设置列宽
    ws.Columns("A:H").AutoFit

    ' 设置标题行格式
    With ws.Range("A1:H1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .HorizontalAlignment = xlCenter
    End With

    MsgBox "测试数据创建完成！共创建了 " & (row - 2) & " 条学生记录。", vbInformation, "测试数据"
End Sub

' 添加学生数据的辅助函数
Sub AddStudentData(ws As Worksheet, ByRef row As Integer, school As String, campus As String, grade As String, className As String, examNumber As String, studentName As String)
    ws.Cells(row, 1).Value = school
    ws.Cells(row, 2).Value = campus
    ws.Cells(row, 3).Value = grade
    ws.Cells(row, 4).Value = className
    ws.Cells(row, 5).Value = examNumber
    ws.Cells(row, 6).Value = studentName
    row = row + 1
End Sub

' 测试单人座位编排
Sub TestSingleSeating()
    ' 设置测试参数
    g_WorksheetName = "考场编排"
    g_ExamRoomCapacity = 4  ' 设置较小的考场容量便于测试
    g_SeatingMode = 1       ' 单人座位

    ' 检查测试数据是否存在
    If Not WorksheetExists(g_WorksheetName) Then
        MsgBox "请先运行 CreateTestData() 创建测试数据！", vbExclamation, "测试错误"
        Exit Sub
    End If

    ' 运行编排
    Call ArrangeExamRooms()
    MsgBox "单人座位编排测试完成！", vbInformation, "测试完成"
End Sub

' 测试双人座位编排（跨年级配对）
Sub TestDoubleSeating()
    ' 设置测试参数
    g_WorksheetName = "考场编排"
    g_ExamRoomCapacity = 3  ' 设置较小的考场容量便于测试
    g_SeatingMode = 2       ' 双人座位

    ' 设置年级组合
    g_GradeGroups(1) = "三年级,四年级"
    g_GradeGroups(2) = "五年级,六年级"
    g_GradeGroups(3) = ""
    g_GradeGroups(4) = ""

    ' 检查测试数据是否存在
    If Not WorksheetExists(g_WorksheetName) Then
        MsgBox "请先运行 CreateTestData() 创建测试数据！", vbExclamation, "测试错误"
        Exit Sub
    End If

    ' 运行编排
    Call ArrangeExamRooms()

    ' 显示跨年级配对结果分析
    Call AnalyzeCrossGradeResult()

    MsgBox "双人座位编排测试完成！请查看跨年级配对分析。", vbInformation, "测试完成"
End Sub

' 测试交叉编排功能
Sub TestCrossClassArrangement()
    ' 设置测试参数
    g_WorksheetName = "考场编排"
    g_ExamRoomCapacity = 6  ' 设置较小的考场容量便于观察交叉编排效果
    g_SeatingMode = 1       ' 单人座位

    ' 检查测试数据是否存在
    If Not WorksheetExists(g_WorksheetName) Then
        MsgBox "请先运行 CreateTestData() 创建测试数据！", vbExclamation, "测试错误"
        Exit Sub
    End If

    ' 运行编排
    Call ArrangeExamRooms()

    ' 显示交叉编排结果分析
    Call AnalyzeCrossClassResult()

    MsgBox "交叉编排测试完成！请查看结果分析。", vbInformation, "测试完成"
End Sub

' 分析交叉编排结果
Sub AnalyzeCrossClassResult()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim analysisText As String
    Dim currentSchool As String, currentGrade As String, currentRoom As String
    Dim prevSchool As String, prevGrade As String, prevRoom As String
    Dim classSequence As String

    Set ws = ThisWorkbook.Worksheets("考场编排")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    analysisText = "交叉编排结果分析：" & vbCrLf & vbCrLf

    For i = 2 To lastRow
        currentSchool = ws.Cells(i, 1).Value
        currentGrade = ws.Cells(i, 3).Value
        currentRoom = ws.Cells(i, 7).Value

        ' 检测学校或年级变化
        If currentSchool <> prevSchool Or currentGrade <> prevGrade Or currentRoom <> prevRoom Then
            If classSequence <> "" Then
                analysisText = analysisText & prevSchool & " " & prevGrade & " 考场" & prevRoom & "：" & classSequence & vbCrLf
            End If
            classSequence = ws.Cells(i, 4).Value  ' 重新开始记录班级序列
        Else
            classSequence = classSequence & "-" & ws.Cells(i, 4).Value
        End If

        prevSchool = currentSchool
        prevGrade = currentGrade
        prevRoom = currentRoom
    Next i

    ' 添加最后一组
    If classSequence <> "" Then
        analysisText = analysisText & prevSchool & " " & prevGrade & " 考场" & prevRoom & "：" & classSequence & vbCrLf
    End If

    analysisText = analysisText & vbCrLf & "说明：班级序列显示了学生在考场中的班级分布，" & vbCrLf & "交叉编排成功时应该看到不同班级交替出现。"

    MsgBox analysisText, vbInformation, "交叉编排分析"
End Sub

' 分析跨年级配对结果
Sub AnalyzeCrossGradeResult()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim analysisText As String
    Dim currentRoom As String
    Dim prevRoom As String
    Dim seatPairs As String
    Dim seatNumber As String
    Dim grade As String

    Set ws = ThisWorkbook.Worksheets("考场编排")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    analysisText = "跨年级配对结果分析：" & vbCrLf & vbCrLf

    For i = 2 To lastRow
        currentRoom = ws.Cells(i, 7).Value  ' 考场号
        seatNumber = ws.Cells(i, 8).Value   ' 座位号
        grade = ws.Cells(i, 3).Value        ' 年级

        ' 检测考场变化
        If currentRoom <> prevRoom Then
            If seatPairs <> "" Then
                analysisText = analysisText & "考场" & prevRoom & "：" & seatPairs & vbCrLf
            End If
            seatPairs = ""  ' 重新开始记录座位配对
        End If

        ' 记录座位配对信息
        If InStr(seatNumber, "A") > 0 Then
            ' A座学生
            seatPairs = seatPairs & "[" & Replace(seatNumber, "A", "") & "号桌:" & grade
        ElseIf InStr(seatNumber, "B") > 0 Then
            ' B座学生，完成配对
            seatPairs = seatPairs & "+" & grade & "] "
        Else
            ' 单人座位
            seatPairs = seatPairs & "[" & seatNumber & ":" & grade & "] "
        End If

        prevRoom = currentRoom
    Next i

    ' 添加最后一组
    If seatPairs <> "" Then
        analysisText = analysisText & "考场" & prevRoom & "：" & seatPairs & vbCrLf
    End If

    analysisText = analysisText & vbCrLf & "说明：" & vbCrLf & _
                  "- [桌号:年级A+年级B] 表示同桌的两个学生来自不同年级" & vbCrLf & _
                  "- A座和B座共用相同的桌号" & vbCrLf & _
                  "- 跨年级配对成功时应该看到不同年级在同一桌"

    MsgBox analysisText, vbInformation, "跨年级配对分析"
End Sub

' 清空编排结果
Sub ClearArrangement()
    Dim ws As Worksheet
    Dim lastRow As Long

    If Not WorksheetExists("考场编排") Then
        MsgBox "未找到考场编排工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    Set ws = ThisWorkbook.Worksheets("考场编排")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    If lastRow > 1 Then
        ws.Range("G2:H" & lastRow).ClearContents
        MsgBox "编排结果已清空！", vbInformation, "完成"
    Else
        MsgBox "没有数据需要清空！", vbInformation, "提示"
    End If
End Sub

' 完整测试流程
Sub RunCompleteTest()
    ' 1. 创建测试数据
    Call CreateTestData()

    ' 2. 测试交叉编排
    Call TestCrossClassArrangement()

    ' 3. 清空结果
    Call ClearArrangement()

    ' 4. 测试双人座位编排
    Call TestDoubleSeating()

    MsgBox "完整测试流程结束！", vbInformation, "测试完成"
End Sub

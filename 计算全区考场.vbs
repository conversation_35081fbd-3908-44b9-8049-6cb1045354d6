Sub 计算全区考场()
    ' 声明变量并指定类型
    <PERSON><PERSON> m <PERSON>, x <PERSON>, i <PERSON>
    Di<PERSON> lastRow As Long
    Dim ws As Worksheet

    ' 获取当前工作表
    Set ws = ActiveSheet

    ' 使用更安全的方式获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "E").End(xlUp).Row

    ' 随机数填充
    For i = 2 To lastRow
        ws.Cells(i, 8) = Rnd()
    Next i

    ' 设置初始值
    Sheets("报名汇总").Range(Sheets("报名汇总").Cells(2, 10), Sheets("报名汇总").Cells(2, 12)).FormulaR1C1 = "1"

    ' 获取报名汇总表的最后一行
    x = Sheets("报名汇总").Cells(Sheets("报名汇总").Rows.Count, "C").End(xlUp).Row
    sh = Array("思茅一小思茅一小", "思茅二小思茅二小", "思茅三小思茅三小", "思茅四小校本部", "思茅四小新时代", _
    "思茅五小思茅五小", "思茅六小中心校", "思茅六小土桥", "思茅六小整碗", "思茅六小南岛河", "思茅六中小学部", _
    "思茅七小思茅七小", "倚象小学蚌弄", "倚象小学菠萝", "倚象小学大寨", "倚象小学竜竜", "倚象小学纳吉", _
    "倚象小学石膏箐", "倚象小学下寨", "倚象小学一零一", "倚象小学营盘山", "倚象小学永庆", "倚象小学鱼塘", _
    "倚象小学中心校", "六顺小学中心校", "云仙小学骂木", "云仙小学中心校", "龙潭小学中心校", _
    "思茅港小茨竹林", "思茅港小莲花塘", "思茅港小那澜", "思茅港小中心校", "育英学校育英学校", "博雅公学")
    sh1 = Array(30, 30, 30, 30, 30, 30, 25, 25, 25, 25, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25, 25, 25, 30, 30, 30, 30)

    Sheets("报名汇总").Select
    '以“学校序号”为第一关键字、“考点”为第二关键字、“班级”为第三关键字为关键字排序
    Range("A1:Q" & x).Select
    Selection.Sort Key1:=Range("B2"), Order1:=xlAscending, Key2:=Range("D2"), Order2:=xlAscending, _
    Key3:=Range("E2"), Order3:=xlAscending, Header:=xlGuess, OrderCustom:=1, MatchCase:= _
        False, Orientation:=xlTopToBottom, SortMethod:=xlPinYin
    '排序后，计算名次
    mc = 1 '名次初值
    r = 2 '行号
    bj = Cells(r, 4).Value & Cells(r, 5).Value

    While (Cells(r, 5).Value <> "") '如果班级存在
        If (Cells(r, 4).Value & Cells(r, 5).Value <> bj) Then
            mc = 1
            bj = Cells(r, 4).Value & Cells(r, 5).Value
        End If
        Cells(r, 9).Value = mc '给“考试排序”赋值，“考试排序”
        mc = mc + 1
        r = r + 1
    Wend
    '以“序号”为第一关键字、“考点”为第二关键字、“考试排序”为第三关键字为关键字排序
    Range("A1:Q" & x).Select
    Selection.Sort Key1:=Range("B2"), Order1:=xlAscending, Key2:=Range("D2"), Order2:=xlAscending, _
    Key3:=Range("I2"), Order3:=xlAscending, Header:=xlGuess, OrderCustom:=1, MatchCase:=False, _
    Orientation:=xlTopToBottom, SortMethod:=xlPinYin
   '计算考试号、座位号、考场号
    For m = 2 To x
         y = Application.Match(Sheets("报名汇总").Cells(m, 3).Value & Sheets("报名汇总").Cells(m, 4).Value, sh, 0) - 1

         If Cells(m - 1, 4).Value & Cells(m - 1, 3).Value <> Cells(m, 4) & Cells(m, 3) Then
            Cells(m, 10).Value = 1
            Cells(m, 11).Value = 1
            Cells(m, 12).Value = 1
        Else
            Cells(m, 10).Value = Cells(m - 1, 10).Value + 1 '计算考试号
            Cells(m, 11) = Int(Int(Cells(m, 10).Value - 1) / sh1(y) + 1)  '考场号
        End If


        If Cells(m, 10).Value Mod sh1(y) <> 0 Then
           Cells(m, 12).Value = Cells(m, 10).Value Mod sh1(y)  '座位号
        Else
           Cells(m, 12).Value = sh1(y)  '座位号
        End If
    Next m
End Sub
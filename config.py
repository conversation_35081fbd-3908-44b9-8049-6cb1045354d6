"""
Configuration settings and constants for the scheduled shutdown application.
"""
import os
import json
from pathlib import Path

# Application constants
APP_NAME = "定时关机"
APP_VERSION = "1.0.0"
CONFIG_FILE = "shutdown_config.json"

# Default settings
DEFAULT_CONFIG = {
    # Shutdown settings
    "shutdown_type": "time",  # time, delay, boot, network, idle
    "shutdown_time": "22:00",
    "shutdown_delay": 3600,  # seconds
    "shutdown_boot_time": 14400,  # seconds
    "network_threshold": 50,  # KB/s
    "network_duration": 300,  # seconds
    "idle_time": 1800,  # seconds
    
    # Task settings
    "task_type": "shutdown",  # shutdown, restart, lock, remind, open_file, execute, close
    "task_message": "时间到了！",
    "task_file_path": "",
    "task_program_path": "",
    "task_program_name": "",
    
    # Advanced control settings
    "block_chat": False,
    "block_games": False,
    "block_browsers": False,
    "block_computer_time": False,
    "block_computer_start": "22:00",
    "block_computer_end": "06:00",
    "block_network_time": False,
    "block_network_start": "22:00",
    "block_network_end": "06:00",
    "block_time_change": False,
    "block_task_manager": False,
    "block_registry": False,
    "block_after_shutdown": False,
    "block_after_shutdown_time": 3600,  # seconds
    "block_usb": False,
    
    # Scheduled tasks
    "scheduled_tasks": [],
    
    # UI settings
    "start_minimized": False,
    "minimize_to_tray": True,
    "confirm_shutdown": True,
    "theme": "system",  # system, light, dark
}

# Chat applications to block
CHAT_APPS = [
    "QQ.exe", 
    "WeChat.exe", 
    "TIM.exe", 
    "DingTalk.exe", 
    "Feishu.exe", 
    "Lark.exe", 
    "YY.exe", 
    "UC.exe", 
    "MSN.exe", 
    "POPO.exe",
    "AliWangwang.exe",
    "Skype.exe",
    "Discord.exe",
    "Telegram.exe",
    "WhatsApp.exe"
]

# Game applications to block (predefined list)
GAME_APPS = [
    "League of Legends.exe",
    "Dota2.exe",
    "CSGO.exe",
    "Overwatch.exe",
    "Fortnite.exe",
    "PUBG.exe",
    "GTA5.exe",
    "Apex Legends.exe",
    "Valorant.exe",
    "Minecraft.exe",
    "Steam.exe",
    "EpicGamesLauncher.exe",
    "Battle.net.exe",
    "Origin.exe",
    "Uplay.exe",
    "GOGGalaxy.exe",
    "WorldOfWarcraft.exe",
    "Hearthstone.exe",
    "StarCraft II.exe",
    "Diablo III.exe",
    "Diablo IV.exe",
    "Warcraft III.exe",
    "Heroes of the Storm.exe",
    "Call of Duty.exe",
    "Battlefield.exe",
    "FIFA.exe",
    "NBA2K.exe",
    "Madden.exe",
    "The Sims.exe",
    "Civilization.exe",
    "Age of Empires.exe",
    "Total War.exe",
    "Fallout.exe",
    "Skyrim.exe",
    "Witcher.exe"
]

# Browser applications to block
BROWSER_APPS = [
    "chrome.exe",
    "firefox.exe",
    "msedge.exe",
    "iexplore.exe",
    "opera.exe",
    "brave.exe",
    "Maxthon.exe",
    "360se.exe",
    "360chrome.exe",
    "QQBrowser.exe",
    "sogouexplorer.exe",
    "TheWorld.exe",
    "TT.exe"
]

class Config:
    """Configuration manager for the application."""
    
    def __init__(self):
        """Initialize the configuration manager."""
        self.config_path = Path.home() / "AppData" / "Local" / APP_NAME / CONFIG_FILE
        self.config = DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self):
        """Load configuration from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Update config with loaded values, keeping default values for missing keys
                    for key, value in loaded_config.items():
                        if key in self.config:
                            self.config[key] = value
        except Exception as e:
            print(f"Error loading configuration: {e}")
    
    def save_config(self):
        """Save configuration to file."""
        try:
            # Create directory if it doesn't exist
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def get(self, key, default=None):
        """Get a configuration value."""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value."""
        self.config[key] = value
        self.save_config()
    
    def reset(self):
        """Reset configuration to default values."""
        self.config = DEFAULT_CONFIG.copy()
        self.save_config()

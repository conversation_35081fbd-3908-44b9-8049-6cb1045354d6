# 考场编排工具 - 快速开始指南（重构版 v2.0）

## 5分钟快速上手

### 第一步：准备数据
1. 在WPS表格中创建名为"考场编排"的工作表
2. 设置以下列标题（第1行）：
   ```
   A列：学校    B列：校点    C列：年级    D列：班级
   E列：准考号  F列：姓名    G列：考场号  H列：桌号
   ```
3. 从第2行开始输入学生数据

### 第二步：导入代码
1. 按 `Alt + F11` 打开VBA编辑器
2. 右键点击工程 → 插入 → 模块
3. 将 `考场编排工具重构版.vbs` 中的代码复制粘贴到模块中

### 第三步：运行程序
1. 按 `Alt + F8` 或在VBA编辑器中按 `F5`
2. 选择 `Main` 程序运行
3. 按照对话框提示设置参数

### 第四步：查看结果
- G列显示考场号
- H列显示桌号
- 各学校各校点的考场号独立编排

## 重构版主要变化

### 桌号概念
- **原版**：座位号（1A、1B、2A、2B...）
- **重构版**：桌号（1、1、2、2、3、3...）

### 桌号管理规则
- **单人座位**：每桌坐1人，桌号不重复（1、2、3、4...）
- **双人座位**：每桌坐2人，桌号可重复（1、1、2、2、3、3...）

## 测试功能

### 快速测试
1. 运行 `CreateTestDataV2()` 创建测试数据
2. 运行 `TestSingleSeatingV2()` 测试单人座位编排
3. 运行 `TestDoubleSeatingV2()` 测试双人座位编排
4. 查看编排结果和分析报告

### 参数示例

#### 单人座位设置
- 考场容量：30（桌子数量）
- 座位模式：选择"否"
- 结果：每桌1人，桌号1,2,3,4...不重复

#### 双人座位设置
- 考场容量：30（桌子数量）
- 座位模式：选择"是"
- 年级组合1：三年级,四年级
- 年级组合2：五年级,六年级
- 结果：每桌2人，桌号1,1,2,2,3,3...重复

## 新功能：桌号管理

### 什么是桌号管理？
- 根据座位模式智能分配桌号
- 单人座位：一桌一人，桌号连续不重复
- 双人座位：一桌两人，桌号可以重复

### 桌号管理的优势
- 更符合实际考场布置
- 便于考场管理和监考
- 清晰的桌位分配逻辑

### 如何验证桌号编排？
- 单人模式：运行 `TestSingleSeatingV2()` 查看桌号序列分析
- 双人模式：运行 `TestDoubleSeatingV2()` 查看跨年级配对分析

## 交叉编排功能

### 什么是交叉编排？
- 同一学校、同一校点的不同年级班级学生交替排列
- 避免同年级班级学生集中在一起
- 例如：三1班→三2班→四1班→三1班→三2班→四1班...

### 交叉编排的优势
- 更公平的考试环境
- 减少同年级班级学生之间的干扰
- 自动处理单年级班级情况（不进行交叉）

## 跨年级配对功能

### 什么是跨年级配对？
- 双人座位模式下，同桌的两个学生来自不同年级
- 例如：三年级学生与四年级学生坐同一桌
- 桌号相同，但学生来自不同年级

### 跨年级配对的优势
- 避免同年级学生相互影响
- 更好的考试纪律管理
- 符合混合年级考试要求

## 常用操作

### 重新编排
直接再次运行 `Main()` 即可，程序会自动清空之前的结果

### 清空结果
运行 `ClearArrangementV2()` 清空G列和H列

### 修改工作表名称
在代码中修改：
```vba
g_WorksheetName = "你的工作表名称"
```

### 完整测试流程
```vba
Sub QuickTest()
    ' 1. 创建测试数据
    Call CreateTestDataV2()
    
    ' 2. 测试单人座位
    Call TestSingleSeatingV2()
    
    ' 3. 清空结果
    Call ClearArrangementV2()
    
    ' 4. 测试双人座位
    Call TestDoubleSeatingV2()
End Sub
```

## 注意事项

1. **数据完整性**：确保学校、校点、年级、班级列不为空
2. **工作表名称**：默认为"考场编排"，可在代码中修改
3. **年级格式**：支持"三年级"、"3年级"等格式
4. **备份数据**：建议在编排前备份原始数据
5. **桌号理解**：理解单人/双人座位下桌号的不同含义

## 编排结果示例

### 单人座位编排结果
```
学校A-本部 考场1：桌号 1-2-3-4-5-6
学校A-本部 考场2：桌号 1-2-3-4-5-6
学校B-分校 考场1：桌号 1-2-3-4
```
说明：每个桌号对应一个学生，桌号连续不重复

### 双人座位编排结果
```
学校A-本部 考场1：[1桌:三年级+四年级] [2桌:三年级+四年级] [3桌:五年级+六年级]
学校A-本部 考场2：[1桌:三年级+四年级] [2桌:五年级+六年级]
```
说明：每个桌号对应两个学生，来自不同年级

## 问题排查

### 常见错误
- "未找到工作表"：检查工作表名称是否正确
- "没有数据"：确保从第2行开始有学生数据
- "年级组合错误"：检查年级名称是否与数据匹配
- "桌号异常"：检查考场容量设置是否合理

### 获取帮助
查看完整的 `考场编排工具使用说明重构版.md` 文档获取详细信息。

## 版本对比

| 功能 | 原版 | 重构版 |
|------|------|--------|
| 座位标识 | 座位号(1A,1B) | 桌号(1,1) |
| 单人座位 | 座位号不重复 | 桌号不重复 |
| 双人座位 | A/B座标识 | 桌号重复 |
| 数据列 | 座位号列 | 桌号列 |
| 管理方式 | 座位管理 | 桌位管理 |

---

**提示**：重构版更符合实际考场管理需求，建议优先使用重构版进行考场编排。

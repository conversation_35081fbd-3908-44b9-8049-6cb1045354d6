import os
import pytesseract
from PIL import Image, ImageTk
from fpdf import FPDF
import tkinter as tk
from tkinter import filedialog, ttk
import cv2
import numpy as np

# 设置Tesseract路径（根据实际安装路径修改）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def extract_text_from_image(image_path, roi=None):
    """
    从图片中提取文字
    :param image_path: 图片路径
    :param roi: 可选，感兴趣区域(x,y,w,h)
    :return: 识别出的文字
    """
    try:
        img = Image.open(image_path)
        
        if roi:
            # 裁剪ROI区域
            img = img.crop((roi[0], roi[1], roi[0]+roi[2], roi[1]+roi[3]))
            
        text = pytesseract.image_to_string(img, lang='chi_sim+eng')
        return text.strip()
    except Exception as e:
        print(f"识别图片时出错: {e}")
        return None
        
def preview_and_select_roi(image_path):
    """
    预览图片并让用户选择感兴趣区域
    :param image_path: 图片路径
    :return: 用户选择的区域(x,y,w,h)
    """
    root = tk.Tk()
    root.title("图片预览和区域选择")
    
    # 加载图片
    img = Image.open(image_path)
    img_tk = ImageTk.PhotoImage(img)
    
    # 创建画布
    canvas = tk.Canvas(root, width=img.width, height=img.height)
    canvas.pack()
    canvas.create_image(0, 0, anchor=tk.NW, image=img_tk)
    
    # 选择区域变量
    start_x, start_y = None, None
    rect_id = None
    roi = None
    
    def on_press(event):
        nonlocal start_x, start_y, rect_id
        start_x, start_y = event.x, event.y
        rect_id = canvas.create_rectangle(start_x, start_y, start_x, start_y, outline='red')
    
    def on_drag(event):
        nonlocal rect_id, start_x, start_y
        if rect_id and start_x and start_y:
            canvas.coords(rect_id, start_x, start_y, event.x, event.y)
    
    def on_release(event):
        nonlocal roi, start_x, start_y
        if start_x and start_y:
            x1, y1 = min(start_x, event.x), min(start_y, event.y)
            x2, y2 = max(start_x, event.x), max(start_y, event.y)
            roi = (x1, y1, x2-x1, y2-y1)
            root.destroy()
    
    # 绑定事件
    canvas.bind('<ButtonPress-1>', on_press)
    canvas.bind('<B1-Motion>', on_drag)
    canvas.bind('<ButtonRelease-1>', on_release)
    
    # 添加确认按钮
    btn_frame = ttk.Frame(root)
    btn_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Button(btn_frame, text="确认选择", command=lambda: root.destroy()).pack(side=tk.RIGHT)
    ttk.Button(btn_frame, text="取消", command=lambda: [setattr(root, 'roi', None), root.destroy()]).pack(side=tk.RIGHT)
    
    root.mainloop()
    return roi if hasattr(root, 'roi') else None

def extract_name(text):
    """
    从识别出的文字中提取姓名
    :param text: 识别出的文字
    :return: 姓名
    """
    # 这里可以根据实际情况调整姓名提取逻辑
    lines = text.split('\n')
    for line in lines:
        if "姓名" in line:
            return line.replace("姓名", "").strip()
    return "未命名"

def create_pdf(text, output_path):
    """
    创建PDF文件
    :param text: 要写入PDF的文字内容
    :param output_path: PDF输出路径
    """
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)
    pdf.multi_cell(0, 10, text)
    pdf.output(output_path)

def process_images_to_pdfs(input_dir, output_dir):
    """
    批量处理图片并生成PDF
    :param input_dir: 包含图片的输入目录
    :param output_dir: 输出PDF的目录
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 使用字典存储姓名对应的所有文本内容
    name_to_texts = {}
    
    for filename in os.listdir(input_dir):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
            image_path = os.path.join(input_dir, filename)
            text = extract_text_from_image(image_path)
            if text:
                name = extract_name(text)
                if name not in name_to_texts:
                    name_to_texts[name] = []
                name_to_texts[name].append(text)
    
    # 为每个姓名生成一个合并的PDF
    for name, texts in name_to_texts.items():
        combined_text = '\n\n'.join(texts)  # 用两个换行分隔不同图片的内容
        pdf_path = os.path.join(output_dir, f"{name}.pdf")
        create_pdf(combined_text, pdf_path)
        print(f"已创建: {pdf_path}")

def select_files_or_folder():
    """
    弹出文件选择对话框让用户选择文件夹或文件
    :return: 选择的文件路径列表和输出文件夹路径
    """
    root = tk.Tk()
    root.withdraw()
    
    choice = input("请选择处理方式(1-文件夹 2-文件): ")
    
    if choice == "1":
        input_folder = filedialog.askdirectory(title="选择包含图片的文件夹")
        if not input_folder:
            return None, None
        return None, input_folder
    elif choice == "2":
        file_paths = filedialog.askopenfilenames(
            title="选择图片文件",
            filetypes=[("图片文件", ".png .jpg .jpeg .bmp .gif")]
        )
        if not file_paths:
            return None, None
        return file_paths, None
    else:
        print("无效选择")
        return None, None

if __name__ == "__main__":
    file_paths, input_folder = select_files_or_folder()
    if not file_paths and not input_folder:
        print("未选择任何文件或文件夹")
        exit()
    
    output_folder = "output_pdfs"  # 输出PDF的文件夹
    
    print("开始处理图片...")
    if input_folder:
        process_images_to_pdfs(input_folder, output_folder)
    else:
        # 处理单个或多个文件
        name_to_texts = {}
        for file_path in file_paths:
            # 预览图片并选择区域
            roi = preview_and_select_roi(file_path)
            text = extract_text_from_image(file_path, roi)
            if text:
                name = extract_name(text)
                if name not in name_to_texts:
                    name_to_texts[name] = []
                name_to_texts[name].append(text)
        
        # 为每个姓名生成一个合并的PDF
        for name, texts in name_to_texts.items():
            combined_text = '\n\n'.join(texts)
            pdf_path = os.path.join(output_folder, f"{name}.pdf")
            create_pdf(combined_text, pdf_path)
            print(f"已创建: {pdf_path}")
    
    print("处理完成！")
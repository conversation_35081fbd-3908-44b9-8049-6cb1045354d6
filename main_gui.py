import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, time, timedelta
import psutil
import win32api
import win32con
import win32gui
import win32process
import os
import time
import shutdown_controller
import system_restrictions
from config import Config
from shutdown_scheduler import shutdown_at_time, shutdown_after_delay, shutdown_after_boot, shutdown_on_low_network, shutdown_on_idle
from advanced_control import block_chat_apps, block_specified_apps, block_browsers, block_computer, block_network, block_time_change, block_taskmgr_regedit, block_after_shutdown, block_usb_disk

config = Config()

class ShutdownApp:
    def __init__(self, root):
        self.root = root
        self.root.title(config.get('APP_NAME', '定时关机软件'))
        self.config = Config()
        self.shutdown_controller = shutdown_controller.ShutdownController(self.config)
        self.system_restrictions = system_restrictions.SystemRestrictions(self.config)

        # 创建标签页
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill='both', expand=True)

        # 定时关机标签页
        self.shutdown_tab = ttk.Frame(self.notebook)
        self.create_shutdown_tab()
        self.notebook.add(self.shutdown_tab, text='定时关机')

        # 高级控制标签页
        self.advanced_tab = ttk.Frame(self.notebook)
        self.create_advanced_tab()
        self.notebook.add(self.advanced_tab, text='高级控制')

        # 计划任务标签页
        self.scheduled_tab = ttk.Frame(self.notebook)
        self.create_scheduled_tab()
        self.notebook.add(self.scheduled_tab, text='计划任务')

    def create_shutdown_tab(self):
        self.shutdown_type = tk.StringVar(value='time')
        ttk.Radiobutton(self.shutdown_tab, text='指定时间关机', variable=self.shutdown_type, value='time', command=self.update_entries).grid(row=0, column=0, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='几点:').grid(row=0, column=1, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='分钟:').grid(row=0, column=3, padx=5, pady=5, sticky='w')
        hour_options = [f'{h:02d}' for h in range(24)]
        self.hour_var = tk.StringVar()
        self.hour_var.set(hour_options[0])
        self.hour_list = ttk.Combobox(self.shutdown_tab, textvariable=self.hour_var, values=hour_options)
        self.hour_list.grid(row=0, column=2, padx=5, pady=5)

        minute_options = [f'{m:02d}' for m in range(0, 60, 15)]
        self.minute_var = tk.StringVar()
        self.minute_var.set(minute_options[0])
        self.minute_list = ttk.Combobox(self.shutdown_tab, textvariable=self.minute_var, values=minute_options)
        self.minute_list.grid(row=0, column=4, padx=5, pady=5)
        ttk.Radiobutton(self.shutdown_tab, text='等待多长时间后关机', variable=self.shutdown_type, value='delay', command=self.update_entries).grid(row=1, column=0, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='等待多长时间后关机(秒):').grid(row=1, column=1, padx=5, pady=5, sticky='w')
        self.delay_entry = ttk.Entry(self.shutdown_tab)
        self.delay_entry.grid(row=1, column=2, padx=5, pady=5)
        ttk.Radiobutton(self.shutdown_tab, text='开机多长时间后关机', variable=self.shutdown_type, value='boot', command=self.update_entries).grid(row=2, column=0, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='开机多长时间后关机(秒):').grid(row=2, column=1, padx=5, pady=5, sticky='w')
        self.boot_entry = ttk.Entry(self.shutdown_tab)
        self.boot_entry.grid(row=2, column=2, padx=5, pady=5)
        ttk.Radiobutton(self.shutdown_tab, text='网络速度关机', variable=self.shutdown_type, value='network', command=self.update_entries).grid(row=3, column=0, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='网络速度阈值(KB/s):').grid(row=3, column=1, padx=5, pady=5, sticky='w')
        self.network_threshold_entry = ttk.Entry(self.shutdown_tab)
        self.network_threshold_entry.grid(row=3, column=2, padx=5, pady=5)
        ttk.Label(self.shutdown_tab, text='持续时间(秒):').grid(row=4, column=1, padx=5, pady=5, sticky='w')
        self.network_duration_entry = ttk.Entry(self.shutdown_tab)
        self.network_duration_entry.grid(row=4, column=2, padx=5, pady=5)
        ttk.Radiobutton(self.shutdown_tab, text='无操作关机', variable=self.shutdown_type, value='idle', command=self.update_entries).grid(row=5, column=0, padx=5, pady=5, sticky='w')
        ttk.Label(self.shutdown_tab, text='无操作时间(秒):').grid(row=5, column=1, padx=5, pady=5, sticky='w')
        self.idle_entry = ttk.Entry(self.shutdown_tab)
        self.idle_entry.grid(row=5, column=2, padx=5, pady=5)
        # 初始化输入框状态
        self.update_entries()

        # 统一保存按钮
        ttk.Button(self.shutdown_tab, text='设置', command=self.save_all_shutdown_settings).grid(row=6, column=0, columnspan=3, padx=5, pady=5)

    def update_entries(self):
        selected = self.shutdown_type.get()
        entries = [self.hour_list, self.minute_list, self.delay_entry, self.boot_entry, self.network_threshold_entry, self.network_duration_entry, self.idle_entry]
        for entry in entries:
            entry.config(state='disabled')
        if selected == 'time':
            self.hour_list.config(state='normal')
            self.minute_list.config(state='normal')
        elif selected == 'delay':
            self.delay_entry.config(state='normal')
        elif selected == 'boot':
            self.boot_entry.config(state='normal')
        elif selected == 'network':
            self.network_threshold_entry.config(state='normal')
            self.network_duration_entry.config(state='normal')
        elif selected == 'idle':
            self.idle_entry.config(state='normal')

    def save_all_shutdown_settings(self):
        try:
            selected = self.shutdown_type.get()
            if selected == 'time':
                target_time_str = f'{self.hour_var.get()}:{self.minute_var.get()}'
                self.config.set('shutdown_type', 'time')
                self.config.set('shutdown_time', target_time_str)
                self.shutdown_controller.start_shutdown_timer()
            elif selected == 'delay':
                delay_seconds = int(self.delay_entry.get())
                self.config.set('shutdown_type', 'delay')
                self.config.set('shutdown_delay', delay_seconds)
                self.shutdown_controller.start_shutdown_timer()
            elif selected == 'boot':
                seconds_after_boot = int(self.boot_entry.get())
                self.config.set('shutdown_type', 'boot')
                self.config.set('shutdown_boot_time', seconds_after_boot)
                self.shutdown_controller.start_shutdown_timer()
            elif selected == 'network':
                threshold_kbps = int(self.network_threshold_entry.get())
                duration_seconds = int(self.network_duration_entry.get())
                self.config.set('shutdown_type', 'network')
                self.config.set('network_threshold', threshold_kbps)
                self.config.set('network_duration', duration_seconds)
                self.shutdown_controller.start_shutdown_timer()
            elif selected == 'idle':
                idle_seconds = int(self.idle_entry.get())
                self.config.set('shutdown_type', 'idle')
                self.config.set('idle_time', idle_seconds)
                self.shutdown_controller.start_shutdown_timer()
            messagebox.showinfo('成功', '定时关机设置保存成功')
        except Exception as e:
            messagebox.showerror('错误', str(e))

    def toggle_block_chat(self):
        self.config.set('block_chat', self.block_chat_var.get())
        if self.block_chat_var.get():
            self.system_restrictions.start_restrictions()
        else:
            self.system_restrictions.stop_restrictions()

    def toggle_block_games(self):
        self.config.set('block_games', self.block_games_var.get())
        if self.block_games_var.get():
            self.system_restrictions.start_restrictions()
        else:
            self.system_restrictions.stop_restrictions()

    def toggle_block_browsers(self):
        self.config.set('block_browsers', self.block_browsers_var.get())
        if self.block_browsers_var.get():
            self.system_restrictions.start_restrictions()
        else:
            self.system_restrictions.stop_restrictions()

    def set_block_computer(self):
        start_time = self.block_computer_start_entry.get()
        end_time = self.block_computer_end_entry.get()
        self.config.set('block_computer_time', True)
        self.config.set('block_computer_start', start_time)
        self.config.set('block_computer_end', end_time)
        self.system_restrictions.start_restrictions()

    def set_block_network(self):
        start_time = self.block_network_start_entry.get()
        end_time = self.block_network_end_entry.get()
        self.config.set('block_network_time', True)
        self.config.set('block_network_start', start_time)
        self.config.set('block_network_end', end_time)
        self.system_restrictions.start_restrictions()

    def toggle_block_time_change(self):
        self.config.set('block_time_change', self.block_time_change_var.get())
        if self.block_time_change_var.get():
            self.system_restrictions.start_restrictions()
        else:
            self.system_restrictions.stop_restrictions()

    def toggle_block_taskmgr_regedit(self):
        self.config.set('block_task_manager', self.block_taskmgr_regedit_var.get())
        self.config.set('block_registry', self.block_taskmgr_regedit_var.get())
        if self.block_taskmgr_regedit_var.get():
            self.system_restrictions.start_restrictions()
        else:
            self.system_restrictions.stop_restrictions()

    def create_advanced_tab(self):
        # 禁止聊天软件
        self.block_chat_var = tk.BooleanVar(value=self.config.get('block_chat', False))
        ttk.Checkbutton(self.advanced_tab, text='禁止聊天软件', variable=self.block_chat_var, command=self.toggle_block_chat).grid(row=0, column=0, padx=5, pady=5, sticky='w')
        
        # 禁止游戏软件
        self.block_games_var = tk.BooleanVar(value=self.config.get('block_games', False))
        ttk.Checkbutton(self.advanced_tab, text='禁止游戏软件', variable=self.block_games_var, command=self.toggle_block_games).grid(row=1, column=0, padx=5, pady=5, sticky='w')
        
        # 禁止浏览器
        self.block_browsers_var = tk.BooleanVar(value=self.config.get('block_browsers', False))
        ttk.Checkbutton(self.advanced_tab, text='禁止浏览器', variable=self.block_browsers_var, command=self.toggle_block_browsers).grid(row=2, column=0, padx=5, pady=5, sticky='w')
        
        # 禁止更改系统时间
        self.block_time_change_var = tk.BooleanVar(value=self.config.get('block_time_change', False))
        ttk.Checkbutton(self.advanced_tab, text='禁止更改系统时间', variable=self.block_time_change_var, command=self.toggle_block_time_change).grid(row=3, column=0, padx=5, pady=5, sticky='w')
        
        # 禁止任务管理器和注册表
        self.block_taskmgr_regedit_var = tk.BooleanVar(value=self.config.get('block_task_manager', False))
        ttk.Checkbutton(self.advanced_tab, text='禁止任务管理器和注册表', variable=self.block_taskmgr_regedit_var, command=self.toggle_block_taskmgr_regedit).grid(row=4, column=0, padx=5, pady=5, sticky='w')
        
        # 禁止时间段使用计算机
        ttk.Label(self.advanced_tab, text='禁止使用计算机时间段:').grid(row=5, column=0, padx=5, pady=5, sticky='w')
        self.block_computer_start_entry = ttk.Entry(self.advanced_tab)
        self.block_computer_start_entry.grid(row=5, column=1, padx=5, pady=5)
        self.block_computer_start_entry.insert(0, self.config.get('block_computer_start', '00:00'))
        ttk.Label(self.advanced_tab, text='至').grid(row=5, column=2, padx=5, pady=5)
        self.block_computer_end_entry = ttk.Entry(self.advanced_tab)
        self.block_computer_end_entry.grid(row=5, column=3, padx=5, pady=5)
        self.block_computer_end_entry.insert(0, self.config.get('block_computer_end', '23:59'))
        ttk.Button(self.advanced_tab, text='设置', command=self.set_block_computer).grid(row=5, column=4, padx=5, pady=5)
        
        # 禁止时间段上网
        ttk.Label(self.advanced_tab, text='禁止上网时间段:').grid(row=6, column=0, padx=5, pady=5, sticky='w')
        self.block_network_start_entry = ttk.Entry(self.advanced_tab)
        self.block_network_start_entry.grid(row=6, column=1, padx=5, pady=5)
        self.block_network_start_entry.insert(0, self.config.get('block_network_start', '00:00'))
        ttk.Label(self.advanced_tab, text='至').grid(row=6, column=2, padx=5, pady=5)
        self.block_network_end_entry = ttk.Entry(self.advanced_tab)
        self.block_network_end_entry.grid(row=6, column=3, padx=5, pady=5)
        self.block_network_end_entry.insert(0, self.config.get('block_network_end', '23:59'))
        ttk.Button(self.advanced_tab, text='设置', command=self.set_block_network).grid(row=6, column=4, padx=5, pady=5)
    
    def create_scheduled_tab(self):
        # 计划任务界面可根据需求扩展
        pass

if __name__ == '__main__':
    root = tk.Tk()
    app = ShutdownApp(root)
    root.mainloop()
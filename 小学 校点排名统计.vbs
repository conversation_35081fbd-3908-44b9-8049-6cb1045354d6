Sub 校级成绩汇总统计()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    Dim ws As Worksheet, resultWs As Worksheet
    Dim lastRow As <PERSON>, lastCol As Long
    Dim i As <PERSON>, j <PERSON>, k <PERSON>, m <PERSON>, n <PERSON> Long
    Dim dataArr As Variant, resultArr As Variant
    Dim schoolDict As Object, gradeDict As Object, campusDict As Object
    Dim schoolCampusDict As Object ' 用于存储学校-校点关系
    Dim schoolArr() As String, gradeArr() As String
    Dim schoolCount As <PERSON>, gradeCount As Long
    Dim totalScoreCol <PERSON>, chineseCol <PERSON>, mathCol <PERSON>, englishCol <PERSON>, scienceCol <PERSON>, moralCol As Long
    Dim gradeCol As <PERSON>, schoolCol As Long, nameCol <PERSON> Long, campusCol As Long
    Dim resultRow As Long

    ' 创建字典对象用于收集唯一值
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set gradeDict = CreateObject("Scripting.Dictionary")
    Set campusDict = CreateObject("Scripting.Dictionary")
    Set schoolCampusDict = CreateObject("Scripting.Dictionary") ' 学校-校点关系字典
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("成绩汇总表")
    Set resultWs = Sheets("校点统计") ' 修改为新的目标表
    
    ' 删除第5行以后的所有行
    If resultWs.Cells(resultWs.rows.Count, 1).End(xlUp).row > 4 Then
        resultWs.rows("5:" & resultWs.rows.Count).Delete
    End If
    
    ' 找到最后一行和最后一列
    lastRow = ws.Cells(ws.rows.Count, 1).End(xlUp).row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    ' 一次性读取所有数据到数组
    dataArr = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value
    
    ' 找到关键列的位置
    For i = 1 To lastCol
        Select Case dataArr(1, i)
            Case "年级": gradeCol = i
            Case "学校": schoolCol = i
            Case "姓名": nameCol = i
            Case "校点", "校区": campusCol = i ' 添加校点列识别
            Case "语文": chineseCol = i
            Case "数学": mathCol = i
            Case "英语": englishCol = i
            Case "科学": scienceCol = i
            Case "道德与法治": moralCol = i
            Case "总分": totalScoreCol = i
        End Select
    Next i
    
    ' 收集唯一的学校、年级和校点，并建立学校-校点关系
    For i = 2 To UBound(dataArr, 1)
        Dim currentSchool As String, currentGrade As String, currentCampus As String
        
        currentSchool = Trim(CStr(dataArr(i, schoolCol)))
        currentGrade = Trim(CStr(dataArr(i, gradeCol)))
        
        If campusCol > 0 Then
            currentCampus = Trim(CStr(dataArr(i, campusCol)))
        Else
            currentCampus = ""
        End If
        
        If Len(currentSchool) > 0 Then
            schoolDict(currentSchool) = 1
            
            ' 建立学校-校点关系
            If Len(currentCampus) > 0 Then
                Dim schoolCampusKey As String
                schoolCampusKey = currentSchool & "|" & currentGrade
                
                If Not schoolCampusDict.Exists(schoolCampusKey) Then
                    Set schoolCampusDict(schoolCampusKey) = CreateObject("Scripting.Dictionary")
                End If
                
                schoolCampusDict(schoolCampusKey)(currentCampus) = 1
                campusDict(currentCampus) = 1 ' 收集所有唯一校点
            End If
        End If
        
        If Len(currentGrade) > 0 Then
            gradeDict(currentGrade) = 1
        End If
    Next i
    
    ' 转换字典键为数组
    schoolCount = schoolDict.Count
    gradeCount = gradeDict.Count
    
    ReDim schoolArr(1 To schoolCount)
    ReDim gradeArr(1 To gradeCount)
    
    i = 1
    For Each school In schoolDict.Keys
        schoolArr(i) = school
        i = i + 1
    Next
    
    i = 1
    For Each grade In gradeDict.Keys
        gradeArr(i) = grade
        i = i + 1
    Next
    
    ' 排序年级数组
    Call QuickSortArray(gradeArr, 1, gradeCount)
    
    ' 预分配结果数组 - 增加一列用于校点
    Dim maxResultRows As Long
    maxResultRows = gradeCount * (schoolCount + 2) * 3 ' 每个年级的学校数+全区+空行，乘以3考虑到可能有多个校点
    ReDim resultArr(1 To maxResultRows, 1 To 25) ' 增加一列用于校点
    resultRow = 1
    
    ' 对每个年级进行统计
    For i = 1 To gradeCount
        Dim currentGradeStr As String
        currentGradeStr = gradeArr(i)
        
        ' 创建统计数组
        Dim stats() As Double
        ReDim stats(1 To schoolCount + 1, 1 To 20)
        
        ' 创建校点统计数组
        Dim campusStats() As Variant
        ReDim campusStats(1 To schoolCount, 1 To 100, 0 To 20) ' 修改第三维从0开始，用于存储校点名称和统计数据
        Dim campusCounts() As Long
        ReDim campusCounts(1 To schoolCount) ' 记录每个学校的校点数量
        
        ' 初始化统计数组
        For j = 1 To schoolCount + 1
            stats(j, 5) = 9999 ' 最低分初始化为很大的值
        Next j
        
        ' 初始化校点统计数组
        For j = 1 To schoolCount
            For k = 1 To 100
                campusStats(j, k, 5) = 9999 ' 最低分初始化为很大的值
            Next k
        Next j
        
        ' 一次性统计所有数据
        For j = 2 To UBound(dataArr, 1)
            If dataArr(j, gradeCol) = currentGradeStr Then
                ' 找到当前学校的索引
                Dim schoolIndex As Long
                schoolIndex = 0
                
                For k = 1 To schoolCount
                    If dataArr(j, schoolCol) = schoolArr(k) Then
                        schoolIndex = k
                        Exit For
                    End If
                Next k
                
                If schoolIndex > 0 Then
                    ' 获取当前校点
                    Dim studentCampus As String
                    If campusCol > 0 Then
                        studentCampus = Trim(CStr(dataArr(j, campusCol)))
                    Else
                        studentCampus = ""
                    End If
                    
                    ' 找到或创建校点索引
                    Dim campusIndex As Long
                    campusIndex = 0

                    If Len(studentCampus) > 0 Then
                        For k = 1 To campusCounts(schoolIndex)
                            If campusStats(schoolIndex, k, 0) = studentCampus Then
                                campusIndex = k
                                Exit For
                            End If
                        Next k
                        
                        If campusIndex = 0 And campusCounts(schoolIndex) < 100 Then ' 确保不超过最大校点数
                            campusCounts(schoolIndex) = campusCounts(schoolIndex) + 1
                            campusIndex = campusCounts(schoolIndex)
                            campusStats(schoolIndex, campusIndex, 0) = studentCampus
                        End If
                    End If
                    
                    ' 处理总分
                    If Len(Trim(CStr(dataArr(j, totalScoreCol)))) > 0 And IsNumeric(dataArr(j, totalScoreCol)) Then
                        Dim score As Double
                        score = CDbl(dataArr(j, totalScoreCol))
                        
                        ' 学校统计
                        stats(schoolIndex, 1) = stats(schoolIndex, 1) + 1 ' 总人数
                        stats(schoolIndex, 2) = stats(schoolIndex, 2) + score ' 总分
                        
                        ' 校点统计
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 1) = campusStats(schoolIndex, campusIndex, 1) + 1 ' 总人数
                            campusStats(schoolIndex, campusIndex, 2) = campusStats(schoolIndex, campusIndex, 2) + score ' 总分
                        End If
                        
                        ' 全区统计
                        stats(schoolCount + 1, 1) = stats(schoolCount + 1, 1) + 1
                        stats(schoolCount + 1, 2) = stats(schoolCount + 1, 2) + score
                        
                        ' 优秀、合格、低分统计
                        If score >= 425 Then
                            stats(schoolIndex, 6) = stats(schoolIndex, 6) + 1 ' 优秀人数
                            stats(schoolCount + 1, 6) = stats(schoolCount + 1, 6) + 1
                            If campusIndex > 0 Then
                                campusStats(schoolIndex, campusIndex, 6) = campusStats(schoolIndex, campusIndex, 6) + 1
                            End If
                        End If
                        
                        If score >= 300 Then
                            stats(schoolIndex, 7) = stats(schoolIndex, 7) + 1 ' 合格人数
                            stats(schoolCount + 1, 7) = stats(schoolCount + 1, 7) + 1
                            If campusIndex > 0 Then
                                campusStats(schoolIndex, campusIndex, 7) = campusStats(schoolIndex, campusIndex, 7) + 1
                            End If
                        End If
                        
                        If score < 200 Then
                            stats(schoolIndex, 8) = stats(schoolIndex, 8) + 1 ' 低分人数
                            stats(schoolCount + 1, 8) = stats(schoolCount + 1, 8) + 1
                            If campusIndex > 0 Then
                                campusStats(schoolIndex, campusIndex, 8) = campusStats(schoolIndex, campusIndex, 8) + 1
                            End If
                        End If
                        
                        ' 最高分和最低分
                        If score > stats(schoolIndex, 4) Then
                            stats(schoolIndex, 4) = score ' 最高分
                        End If
                        
                        If score < stats(schoolIndex, 5) Then
                            stats(schoolIndex, 5) = score ' 最低分
                        End If
                        
                        ' 校点最高分和最低分
                        If campusIndex > 0 Then
                            If score > campusStats(schoolIndex, campusIndex, 4) Then
                                campusStats(schoolIndex, campusIndex, 4) = score ' 最高分
                            End If
                            
                            If score < campusStats(schoolIndex, campusIndex, 5) Then
                                campusStats(schoolIndex, campusIndex, 5) = score ' 最低分
                            End If
                        End If
                        
                        ' 全区最高分和最低分
                        If score > stats(schoolCount + 1, 4) Then
                            stats(schoolCount + 1, 4) = score
                        End If
                        
                        If score < stats(schoolCount + 1, 5) Then
                            stats(schoolCount + 1, 5) = score
                        End If
                    End If
                    
                    ' 处理各科目
                    ' 语文
                    If Len(Trim(CStr(dataArr(j, chineseCol)))) > 0 And IsNumeric(dataArr(j, chineseCol)) Then
                        Dim chineseScore As Double
                        chineseScore = CDbl(dataArr(j, chineseCol))
                        
                        stats(schoolIndex, 9) = stats(schoolIndex, 9) + chineseScore ' 语文总分
                        stats(schoolIndex, 10) = stats(schoolIndex, 10) + 1 ' 语文人数
                        stats(schoolCount + 1, 9) = stats(schoolCount + 1, 9) + chineseScore
                        stats(schoolCount + 1, 10) = stats(schoolCount + 1, 10) + 1
                        
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 9) = campusStats(schoolIndex, campusIndex, 9) + chineseScore
                            campusStats(schoolIndex, campusIndex, 10) = campusStats(schoolIndex, campusIndex, 10) + 1
                        End If
                    End If
                    
                    ' 数学
                    If Len(Trim(CStr(dataArr(j, mathCol)))) > 0 And IsNumeric(dataArr(j, mathCol)) Then
                        Dim mathScore As Double
                        mathScore = CDbl(dataArr(j, mathCol))
                        
                        stats(schoolIndex, 11) = stats(schoolIndex, 11) + mathScore
                        stats(schoolIndex, 12) = stats(schoolIndex, 12) + 1
                        stats(schoolCount + 1, 11) = stats(schoolCount + 1, 11) + mathScore
                        stats(schoolCount + 1, 12) = stats(schoolCount + 1, 12) + 1
                        
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 11) = campusStats(schoolIndex, campusIndex, 11) + mathScore
                            campusStats(schoolIndex, campusIndex, 12) = campusStats(schoolIndex, campusIndex, 12) + 1
                        End If
                    End If
                    
                    ' 英语
                    If Len(Trim(CStr(dataArr(j, englishCol)))) > 0 And IsNumeric(dataArr(j, englishCol)) Then
                        Dim englishScore As Double
                        englishScore = CDbl(dataArr(j, englishCol))
                        
                        stats(schoolIndex, 13) = stats(schoolIndex, 13) + englishScore
                        stats(schoolIndex, 14) = stats(schoolIndex, 14) + 1
                        stats(schoolCount + 1, 13) = stats(schoolCount + 1, 13) + englishScore
                        stats(schoolCount + 1, 14) = stats(schoolCount + 1, 14) + 1
                        
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 13) = campusStats(schoolIndex, campusIndex, 13) + englishScore
                            campusStats(schoolIndex, campusIndex, 14) = campusStats(schoolIndex, campusIndex, 14) + 1
                        End If
                    End If
                    
                    ' 科学
                    If Len(Trim(CStr(dataArr(j, scienceCol)))) > 0 And IsNumeric(dataArr(j, scienceCol)) Then
                        Dim scienceScore As Double
                        scienceScore = CDbl(dataArr(j, scienceCol))
                        
                        stats(schoolIndex, 15) = stats(schoolIndex, 15) + scienceScore
                        stats(schoolIndex, 16) = stats(schoolIndex, 16) + 1 ' 科学人数
                        stats(schoolCount + 1, 15) = stats(schoolCount + 1, 15) + scienceScore
                        stats(schoolCount + 1, 16) = stats(schoolCount + 1, 16) + 1
                        
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 15) = campusStats(schoolIndex, campusIndex, 15) + scienceScore
                            campusStats(schoolIndex, campusIndex, 16) = campusStats(schoolIndex, campusIndex, 16) + 1
                        End If
                    End If
                    
                    ' 道德与法治
                    If Len(Trim(CStr(dataArr(j, moralCol)))) > 0 And IsNumeric(dataArr(j, moralCol)) Then
                        Dim moralScore As Double
                        moralScore = CDbl(dataArr(j, moralCol))
                        
                        stats(schoolIndex, 17) = stats(schoolIndex, 17) + moralScore
                        stats(schoolIndex, 18) = stats(schoolIndex, 18) + 1
                        stats(schoolCount + 1, 17) = stats(schoolCount + 1, 17) + moralScore
                        stats(schoolCount + 1, 18) = stats(schoolCount + 1, 18) + 1
                        
                        If campusIndex > 0 Then
                            campusStats(schoolIndex, campusIndex, 17) = campusStats(schoolIndex, campusIndex, 17) + moralScore
                            campusStats(schoolIndex, campusIndex, 18) = campusStats(schoolIndex, campusIndex, 18) + 1
                        End If
                    End If
                End If
            End If
        Next j
        
        ' 直接填充校点数据，跳过学校汇总数据
        For j = 1 To schoolCount
            ' 填充各校点数据
            For k = 1 To campusCounts(j)
                If campusStats(j, k, 1) > 0 Then ' 只输出有学生的校点
                    ' 年级
                    resultArr(resultRow, 1) = currentGradeStr
                    ' 学校
                    resultArr(resultRow, 2) = schoolArr(j)
                    ' 校点
                    resultArr(resultRow, 3) = campusStats(j, k, 0)
                    ' 参考人数
                    resultArr(resultRow, 4) = campusStats(j, k, 1)
                    
                    ' 总分平均分
                    If campusStats(j, k, 1) > 0 Then
                        resultArr(resultRow, 5) = Round(campusStats(j, k, 2) / campusStats(j, k, 1), 2)
                    Else
                        resultArr(resultRow, 5) = 0
                    End If
                    
                    ' 计算校点排名 - 添加校点排名逻辑
                    Dim campusRank As Long
                    campusRank = 1
                    
                    ' 计算该校点在当前年级中的排名
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 1) > 0 Then
                                ' 计算比较校点的平均分
                                Dim otherAvg As Double
                                If campusStats(m, n, 1) > 0 Then
                                    otherAvg = campusStats(m, n, 2) / campusStats(m, n, 1)
                                    
                                    ' 如果其他校点平均分更高，排名+1
                                    If otherAvg > (campusStats(j, k, 2) / campusStats(j, k, 1)) Then
                                        campusRank = campusRank + 1
                                    End If
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 总分排名 - 校点参与排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 6) = campusRank
                    Else
                        resultArr(resultRow, 6) = "-"
                    End If
                    
                    ' 总分优秀人数
                    resultArr(resultRow, 7) = campusStats(j, k, 6)
                    
                    ' 总分优秀比例%
                    If campusStats(j, k, 1) > 0 Then
                        resultArr(resultRow, 8) = Round(campusStats(j, k, 6) / campusStats(j, k, 1) * 100, 2)
                    Else
                        resultArr(resultRow, 8) = 0
                    End If
                    
                    ' 总分合格人数
                    resultArr(resultRow, 9) = campusStats(j, k, 7)
                    
                    ' 总分合格比例%
                    If campusStats(j, k, 1) > 0 Then
                        resultArr(resultRow, 10) = Round(campusStats(j, k, 7) / campusStats(j, k, 1) * 100, 2)
                    Else
                        resultArr(resultRow, 10) = 0
                    End If
                    
                    ' 总分低分人数
                    resultArr(resultRow, 11) = campusStats(j, k, 8)
                    
                    ' 总分低分比例%
                    If campusStats(j, k, 1) > 0 Then
                        resultArr(resultRow, 12) = Round(campusStats(j, k, 8) / campusStats(j, k, 1) * 100, 2)
                    Else
                        resultArr(resultRow, 12) = 0
                    End If
                    
                    ' 总分最高分
                    resultArr(resultRow, 13) = campusStats(j, k, 4)
                    
                    ' 总分最低分
                    If campusStats(j, k, 5) = 9999 Then
                        resultArr(resultRow, 14) = 0
                    Else
                        resultArr(resultRow, 14) = campusStats(j, k, 5)
                    End If
                    
                    ' 计算校点各科平均分
                    Dim campusAvgChinese As Double, campusAvgMath As Double, campusAvgEnglish As Double
                    Dim campusAvgScience As Double, campusAvgMoral As Double
                    
                    If campusStats(j, k, 10) > 0 Then
                        campusAvgChinese = campusStats(j, k, 9) / campusStats(j, k, 10)
                    End If
                    
                    If campusStats(j, k, 12) > 0 Then
                        campusAvgMath = campusStats(j, k, 11) / campusStats(j, k, 12)
                    End If
                    
                    If campusStats(j, k, 14) > 0 Then
                        campusAvgEnglish = campusStats(j, k, 13) / campusStats(j, k, 14)
                    End If
                    
                    If campusStats(j, k, 16) > 0 Then
                        campusAvgScience = campusStats(j, k, 15) / campusStats(j, k, 16)
                    End If
                    
                    If campusStats(j, k, 18) > 0 Then
                        campusAvgMoral = campusStats(j, k, 17) / campusStats(j, k, 18)
                    End If
                    
                    ' 各科平均分
                    resultArr(resultRow, 15) = Round((campusAvgChinese + campusAvgMath + campusAvgEnglish + campusAvgScience + campusAvgMoral) / 5, 2)
                    
                    ' 语文平均分
                    resultArr(resultRow, 16) = Round(campusAvgChinese, 2)
                    
                    ' 计算语文排名
                    Dim chineseRank As Long
                    chineseRank = 1
                    
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 10) > 0 Then
                                Dim otherChineseAvg As Double
                                otherChineseAvg = campusStats(m, n, 9) / campusStats(m, n, 10)
                                
                                If otherChineseAvg > campusAvgChinese Then
                                    chineseRank = chineseRank + 1
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 语文平均分排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 17) = chineseRank
                    Else
                        resultArr(resultRow, 17) = "-"
                    End If
                    
                    ' 数学平均分
                    resultArr(resultRow, 18) = Round(campusAvgMath, 2)
                    
                    ' 计算数学排名
                    Dim mathRank As Long
                    mathRank = 1
                    
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 12) > 0 Then
                                Dim otherMathAvg As Double
                                otherMathAvg = campusStats(m, n, 11) / campusStats(m, n, 12)
                                
                                If otherMathAvg > campusAvgMath Then
                                    mathRank = mathRank + 1
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 数学平均分排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 19) = mathRank
                    Else
                        resultArr(resultRow, 19) = "-"
                    End If
                    
                    ' 英语平均分
                    resultArr(resultRow, 20) = Round(campusAvgEnglish, 2)
                    
                    ' 计算英语排名
                    Dim englishRank As Long
                    englishRank = 1
                    
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 14) > 0 Then
                                Dim otherEnglishAvg As Double
                                otherEnglishAvg = campusStats(m, n, 13) / campusStats(m, n, 14)
                                
                                If otherEnglishAvg > campusAvgEnglish Then
                                    englishRank = englishRank + 1
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 英语平均分排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 21) = englishRank
                    Else
                        resultArr(resultRow, 21) = "-"
                    End If
                    
                    ' 科学平均分
                    resultArr(resultRow, 22) = Round(campusAvgScience, 2)
                    
                    ' 计算科学排名
                    Dim scienceRank As Long
                    scienceRank = 1
                    
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 16) > 0 Then
                                Dim otherScienceAvg As Double
                                otherScienceAvg = campusStats(m, n, 15) / campusStats(m, n, 16)
                                
                                If otherScienceAvg > campusAvgScience Then
                                    scienceRank = scienceRank + 1
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 科学平均分排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 23) = scienceRank
                    Else
                        resultArr(resultRow, 23) = "-"
                    End If
                    
                    ' 道法平均分
                    resultArr(resultRow, 24) = Round(campusAvgMoral, 2)
                    
                    ' 计算道法排名
                    Dim moralRank As Long
                    moralRank = 1
                    
                    For m = 1 To schoolCount
                        For n = 1 To campusCounts(m)
                            ' 跳过自己和育英学校
                            If (m <> j Or n <> k) And schoolArr(m) <> "育英学校" And campusStats(m, n, 18) > 0 Then
                                Dim otherMoralAvg As Double
                                otherMoralAvg = campusStats(m, n, 17) / campusStats(m, n, 18)
                                
                                If otherMoralAvg > campusAvgMoral Then
                                    moralRank = moralRank + 1
                                End If
                            End If
                        Next n
                    Next m
                    
                    ' 道法平均分排名
                    If schoolArr(j) <> "育英学校" Then
                        resultArr(resultRow, 25) = moralRank
                    Else
                        resultArr(resultRow, 25) = "-"
                    End If
                    
                    resultRow = resultRow + 1
                End If
            Next k
        Next j
        
        ' 添加全区统计行
        ' 年级
        resultArr(resultRow, 1) = currentGradeStr
        ' 学校
        resultArr(resultRow, 2) = "全区"
        ' 校点
        resultArr(resultRow, 3) = "汇总"
        ' 参考人数
        resultArr(resultRow, 4) = stats(schoolCount + 1, 1)
        
        ' 全区总分平均分
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 5) = Round(stats(schoolCount + 1, 2) / stats(schoolCount + 1, 1), 2)
        Else
            resultArr(resultRow, 5) = 0
        End If
        
        ' 全区不参与排名
        resultArr(resultRow, 6) = "-"
        
        ' 总分优秀人数
        resultArr(resultRow, 7) = stats(schoolCount + 1, 6)
        
        ' 总分优秀比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 8) = Round(stats(schoolCount + 1, 6) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 8) = 0
        End If
        
        ' 总分合格人数
        resultArr(resultRow, 9) = stats(schoolCount + 1, 7)
        
        ' 总分合格比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 10) = Round(stats(schoolCount + 1, 7) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 10) = 0
        End If
        
        ' 总分低分人数
        resultArr(resultRow, 11) = stats(schoolCount + 1, 8)
        
        ' 总分低分比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 12) = Round(stats(schoolCount + 1, 8) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 12) = 0
        End If
        
        ' 总分最高分
        resultArr(resultRow, 13) = stats(schoolCount + 1, 4)
        
        ' 总分最低分
        If stats(schoolCount + 1, 5) = 9999 Then
            resultArr(resultRow, 14) = 0
        Else
            resultArr(resultRow, 14) = stats(schoolCount + 1, 5)
        End If
        
        ' 全区各科平均分
        Dim avgChinese As Double, avgMath As Double, avgEnglish As Double, avgScience As Double, avgMoral As Double
        
        If stats(schoolCount + 1, 10) > 0 Then
            avgChinese = stats(schoolCount + 1, 9) / stats(schoolCount + 1, 10)
        End If
        
        If stats(schoolCount + 1, 12) > 0 Then
            avgMath = stats(schoolCount + 1, 11) / stats(schoolCount + 1, 12)
        End If
        
        If stats(schoolCount + 1, 14) > 0 Then
            avgEnglish = stats(schoolCount + 1, 13) / stats(schoolCount + 1, 14)
        End If
        
        If stats(schoolCount + 1, 16) > 0 Then
            avgScience = stats(schoolCount + 1, 15) / stats(schoolCount + 1, 16)
        End If
        
        If stats(schoolCount + 1, 18) > 0 Then
            avgMoral = stats(schoolCount + 1, 17) / stats(schoolCount + 1, 18)
        End If
        
        resultArr(resultRow, 15) = Round((avgChinese + avgMath + avgEnglish + avgScience + avgMoral) / 5, 2)
        resultArr(resultRow, 16) = Round(avgChinese, 2)
        resultArr(resultRow, 17) = "-" ' 全区不参与排名
        resultArr(resultRow, 18) = Round(avgMath, 2)
        resultArr(resultRow, 19) = "-"
        resultArr(resultRow, 20) = Round(avgEnglish, 2)
        resultArr(resultRow, 21) = "-"
        resultArr(resultRow, 22) = Round(avgScience, 2)
        resultArr(resultRow, 23) = "-"
        resultArr(resultRow, 24) = Round(avgMoral, 2)
        resultArr(resultRow, 25) = "-"
        
        resultRow = resultRow + 1
        
        ' 添加空行
        resultRow = resultRow + 1
    Next i
    
    ' 一次性写入结果
    resultWs.Range("A5").Resize(resultRow - 1, 25).Value = resultArr
    
    ' 格式化结果
    resultWs.Range("H5:H" & resultRow + 4).NumberFormat = "0.00"
    resultWs.Range("J5:J" & resultRow + 4).NumberFormat = "0.00"
    resultWs.Range("L5:L" & resultRow + 4).NumberFormat = "0.00"
    
    ' 调用自定义排序
    Call 自定义排序
    
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    
    MsgBox "统计完成！", vbInformation
End Sub

Sub 自定义排序()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim dataRange As Range
    Dim i As Long, j As Long, k As Long
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("校点统计") ' 修改为新的目标表
    
    ' 找到最后一行
    lastRow = ws.Cells(ws.rows.Count, 1).End(xlUp).row
    
    ' 如果没有数据，则退出
    If lastRow < 6 Then
        MsgBox "没有找到数据！", vbExclamation
        Exit Sub
    End If
    
    ' 设置数据范围
    Set dataRange = ws.Range("A5:Y" & lastRow)
    
    ' 设置区域内文字居中
    dataRange.HorizontalAlignment = xlCenter
    dataRange.VerticalAlignment = xlCenter

    ' WPS兼容的边框设置方式
    dataRange.Borders(xlInsideHorizontal).LineStyle = xlContinuous
    dataRange.Borders(xlInsideHorizontal).Weight = xlThin
    dataRange.Borders(xlInsideVertical).LineStyle = xlContinuous
    dataRange.Borders(xlInsideVertical).Weight = xlThin
    
    dataRange.Borders(xlEdgeLeft).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeLeft).Weight = xlMedium
    dataRange.Borders(xlEdgeTop).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeTop).Weight = xlMedium
    dataRange.Borders(xlEdgeBottom).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeBottom).Weight = xlMedium
    dataRange.Borders(xlEdgeRight).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeRight).Weight = xlMedium
    
    ' 定义年级顺序
    Dim gradeOrder As Object
    Set gradeOrder = CreateObject("Scripting.Dictionary")
    gradeOrder("一") = 1
    gradeOrder("二") = 2
    gradeOrder("三") = 3
    gradeOrder("四") = 4
    gradeOrder("五") = 5
    gradeOrder("六") = 6
    
    ' 定义学校顺序
    Dim schoolOrder As Object
    Set schoolOrder = CreateObject("Scripting.Dictionary")
    schoolOrder("全区") = 1
    schoolOrder("思茅一小") = 2
    schoolOrder("思茅二小") = 3
    schoolOrder("思茅三小") = 4
    schoolOrder("思茅四小") = 5
    schoolOrder("思茅五小") = 6
    schoolOrder("思茅六小") = 7
    schoolOrder("思茅六中") = 8
    schoolOrder("思茅七小") = 9
    schoolOrder("倚象小学") = 10
    schoolOrder("云仙小学") = 11
    schoolOrder("龙潭小学") = 12
    schoolOrder("思茅港小") = 13
    schoolOrder("六顺小学") = 14
    schoolOrder("传薪校区") = 15
    schoolOrder("博雅公学") = 16
    schoolOrder("育英学校") = 17
    
    ' 定义校点顺序
    Dim campusOrder As Object
    Set campusOrder = CreateObject("Scripting.Dictionary")
    campusOrder("汇总") = 1
    campusOrder("本部") = 2
    campusOrder("分校") = 3
    campusOrder("校区") = 4
    
    ' 读取数据到二维数组
    Dim data() As Variant
    ReDim data(1 To lastRow - 4, 1 To 28) ' 25列数据 + 3列排序值
    
    ' 读取数据并添加排序值
    For i = 5 To lastRow
        For j = 1 To 25
            data(i - 4, j) = ws.Cells(i, j).Value
        Next j
        
        ' 添加年级排序值
        If gradeOrder.Exists(CStr(ws.Cells(i, 1).Value)) Then
            data(i - 4, 26) = gradeOrder(CStr(ws.Cells(i, 1).Value))
        Else
            data(i - 4, 26) = 999 ' 未知年级放最后
        End If
        
        ' 添加学校排序值
        If schoolOrder.Exists(CStr(ws.Cells(i, 2).Value)) Then
            data(i - 4, 27) = schoolOrder(CStr(ws.Cells(i, 2).Value))
        Else
            data(i - 4, 27) = 999 ' 未知学校放最后
        End If
        
        ' 添加校点排序值
        If campusOrder.Exists(CStr(ws.Cells(i, 3).Value)) Then
            data(i - 4, 28) = campusOrder(CStr(ws.Cells(i, 3).Value))
        Else
            data(i - 4, 28) = 999 ' 未知校点放最后
        End If
    Next i
    
    ' 使用改进的冒泡排序 - 确保三级排序正确生效
    Dim swapped As Boolean
    Dim temp As Variant
    
    For i = 1 To UBound(data) - 1
        swapped = False
        
        For j = 1 To UBound(data) - i
            ' 比较主键（年级）
            If data(j, 26) > data(j + 1, 26) Then
                ' 交换整行
                For k = 1 To 28
                    temp = data(j, k)
                    data(j, k) = data(j + 1, k)
                    data(j + 1, k) = temp
                Next k
                swapped = True
            ' 如果主键相同，比较次键（学校）
            ElseIf data(j, 26) = data(j + 1, 26) And data(j, 27) > data(j + 1, 27) Then
                ' 交换整行
                For k = 1 To 28
                    temp = data(j, k)
                    data(j, k) = data(j + 1, k)
                    data(j + 1, k) = temp
                Next k
                swapped = True
            ' 如果主键和次键都相同，比较三级键（校点）
            ElseIf data(j, 26) = data(j + 1, 26) And data(j, 27) = data(j + 1, 27) And data(j, 28) > data(j + 1, 28) Then
                ' 交换整行
                For k = 1 To 28
                    temp = data(j, k)
                    data(j, k) = data(j + 1, k)
                    data(j + 1, k) = temp
                Next k
                swapped = True
            End If
        Next j
        
        ' 如果没有交换，说明已经排好序
        If Not swapped Then Exit For
    Next i
    
    ' 将排序后的数据写回工作表
    For i = 1 To UBound(data)
        For j = 1 To 25
            ws.Cells(i + 4, j).Value = data(i, j)
        Next j
    Next i
    
    ' 设置格式
        For i = 5 To lastRow
            ' 设置全区行的格式
            If ws.Cells(i, 2).Value = "全区" Then
                ws.Range(ws.Cells(i, 1), ws.Cells(i, 25)).Interior.Color = RGB(255, 255, 0)
                ws.Range(ws.Cells(i, 1), ws.Cells(i, 25)).Font.Bold = True
            End If
        Next i
End Sub

' 快速排序算法用于排序数组
Sub QuickSortArray(ByRef arr() As String, ByVal first As Long, ByVal last As Long)
    Dim vPivot As Variant
    Dim vTemp As Variant
    Dim low As Long
    Dim high As Long
    
    If first >= last Then Exit Sub
    
    low = first
    high = last
    vPivot = arr((first + last) \ 2)
    
    Do While low <= high
        Do While arr(low) < vPivot And low < last
            low = low + 1
        Loop
        
        Do While vPivot < arr(high) And high > first
            high = high - 1
        Loop
        
        If low <= high Then
            vTemp = arr(low)
            arr(low) = arr(high)
            arr(high) = vTemp
            low = low + 1
            high = high - 1
        End If
    Loop
    
    If first < high Then QuickSortArray arr, first, high
    If low < last Then QuickSortArray arr, low, last
End Sub

' 快速排序算法用于排序排名数据
Sub QuickSortRank(ByRef arr() As Double, ByVal first As Long, ByVal last As Long)
    Dim vPivot As Double
    Dim vTemp1 As Double, vTemp2 As Double
    Dim low As Long
    Dim high As Long
    
    If first >= last Then Exit Sub
    
    low = first
    high = last
    vPivot = arr((first + last) \ 2, 2)
    
    Do While low <= high
        Do While arr(low, 2) > vPivot And low < last  ' 注意这里是降序排列
            low = low + 1
        Loop
        
        Do While vPivot > arr(high, 2) And high > first
            high = high - 1
        Loop
        
        If low <= high Then
            ' 交换两个值
            vTemp1 = arr(low, 1)
            vTemp2 = arr(low, 2)
            arr(low, 1) = arr(high, 1)
            arr(low, 2) = arr(high, 2)
            arr(high, 1) = vTemp1
            arr(high, 2) = vTemp2
            low = low + 1
            high = high - 1
        End If
    Loop
    
    If first < high Then QuickSortRank arr, first, high
    If low < last Then QuickSortRank arr, low, last
End Sub

' 快速排序算法用于排序校点排名数据
Sub QuickSortCampusRank(ByRef arr() As Double, ByVal first As Long, ByVal last As Long)
    Dim vPivot As Double
    Dim vTemp1 As Double, vTemp2 As Double, vTemp3 As Double
    Dim low As Long
    Dim high As Long
    
    If first >= last Then Exit Sub
    
    low = first
    high = last
    vPivot = arr((first + last) \ 2, 3)
    
    Do While low <= high
        Do While arr(low, 3) > vPivot And low < last  ' 注意这里是降序排列
            low = low + 1
        Loop
        
        Do While vPivot > arr(high, 3) And high > first
            high = high - 1
        Loop
        
        If low <= high Then
            ' 交换三个值
            vTemp1 = arr(low, 1)
            vTemp2 = arr(low, 2)
            vTemp3 = arr(low, 3)
            arr(low, 1) = arr(high, 1)
            arr(low, 2) = arr(high, 2)
            arr(low, 3) = arr(high, 3)
            arr(high, 1) = vTemp1
            arr(high, 2) = vTemp2
            arr(high, 3) = vTemp3
            low = low + 1
            high = high - 1
        End If
    Loop
    
    If first < high Then QuickSortCampusRank arr, first, high
    If low < last Then QuickSortCampusRank arr, low, last
End Sub
"""
Utility functions for the scheduled shutdown application.
"""
import os
import sys
import time
import logging
import datetime
import psutil
from pathlib import Path

def setup_logging():
    """Set up logging for the application."""
    log_dir = Path.home() / "AppData" / "Local" / "定时关机" / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / f"shutdown_{datetime.datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger("定时关机")

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes}分钟{seconds}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours}小时{minutes}分钟{seconds}秒"

def parse_time(time_str):
    """Parse a time string (HH:MM) into a datetime.time object."""
    try:
        hours, minutes = map(int, time_str.split(':'))
        return datetime.time(hours, minutes)
    except ValueError:
        return None

def get_boot_time():
    """Get the system boot time."""
    return datetime.datetime.fromtimestamp(psutil.boot_time())

def get_uptime():
    """Get the system uptime in seconds."""
    boot_time = get_boot_time()
    now = datetime.datetime.now()
    return (now - boot_time).total_seconds()

def get_network_speed():
    """Get the current network speed in KB/s."""
    net_io_before = psutil.net_io_counters()
    time.sleep(1)
    net_io_after = psutil.net_io_counters()
    
    # Calculate bytes sent and received in the last second
    bytes_sent = net_io_after.bytes_sent - net_io_before.bytes_sent
    bytes_recv = net_io_after.bytes_recv - net_io_before.bytes_recv
    
    # Convert to KB/s
    kb_sent = bytes_sent / 1024
    kb_recv = bytes_recv / 1024
    
    return kb_sent + kb_recv  # Total network speed

def is_admin():
    """Check if the application is running with administrator privileges."""
    try:
        return os.getuid() == 0
    except AttributeError:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0

def run_as_admin():
    """Restart the application with administrator privileges."""
    if not is_admin():
        import ctypes
        ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
        sys.exit(0)

def resource_path(relative_path):
    """Get the absolute path to a resource file."""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def get_process_list():
    """Get a list of running processes."""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'username']):
        try:
            processes.append({
                'pid': proc.info['pid'],
                'name': proc.info['name'],
                'username': proc.info['username']
            })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return processes

def kill_process(process_name):
    """Kill a process by name."""
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'].lower() == process_name.lower():
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

"""
Keyboard/mouse idle monitoring functionality.
"""
import time
import logging
import threading
import win32api
import win32gui

logger = logging.getLogger("定时关机")

class IdleMonitor:
    """Monitor keyboard/mouse idle time and detect when idle for a duration."""
    
    def __init__(self, idle_seconds, callback=None):
        """Initialize the idle monitor.
        
        Args:
            idle_seconds: The idle time threshold in seconds.
            callback: A function to call when the system is idle for the specified duration.
        """
        self.idle_seconds = idle_seconds
        self.callback = callback
        self.monitor_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
    
    def start(self):
        """Start monitoring idle time."""
        if self.is_running:
            return False
        
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_thread, daemon=True)
        self.monitor_thread.start()
        self.is_running = True
        
        logger.info(f"Started idle monitoring (threshold: {self.idle_seconds}s)")
        return True
    
    def stop(self):
        """Stop monitoring idle time."""
        if not self.is_running:
            return False
        
        self.stop_event.set()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.is_running = False
        
        logger.info("Stopped idle monitoring")
        return True
    
    def _monitor_thread(self):
        """Thread function to monitor idle time."""
        while not self.stop_event.is_set():
            try:
                idle_time = self._get_idle_time()
                
                if idle_time >= self.idle_seconds:
                    logger.info(f"System idle for {self.idle_seconds}s")
                    if self.callback:
                        self.callback()
                    break
                
                # Sleep for a shorter time when approaching the threshold
                sleep_time = min(1.0, max(0.1, self.idle_seconds - idle_time))
                time.sleep(sleep_time)
            except Exception as e:
                logger.error(f"Error monitoring idle time: {e}")
                time.sleep(5.0)  # Wait a bit longer on error
    
    def _get_idle_time(self):
        """Get the current idle time in seconds."""
        # GetLastInputInfo returns the last input time in milliseconds
        # GetTickCount returns the system uptime in milliseconds
        # The difference is the idle time in milliseconds
        return (win32api.GetTickCount() - win32gui.GetLastInputInfo()) / 1000.0
    
    def get_current_idle_time(self):
        """Get the current idle time in seconds."""
        return self._get_idle_time()

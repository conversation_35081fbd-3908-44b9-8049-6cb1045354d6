Sub 分数段以上()
    
    Dim QP(1 To 180, 1 To 19)
    Dim arr, brr, crr, drr(), p, s, 总大小, 间隔, x, y, i, j, k, l As Integer
    Dim temp1, temp2 As Single
    ReDim drr(1 To 15, 1 To 9)
    
    crr = Array("思茅一中", "普洱二中", "思茅三中", "思茅四中", "思茅六中", "逸夫中学", "云仙中学", "龙潭中学", "思茅港中", "六顺中学", "育英学校", "博雅公学", "普洱市民中", "普洱市一中")
    Application.DisplayAlerts = False
    o = Sheets("成绩总表").Range("C65536").End(xlUp).Row
    
    arr = Sheets("成绩总表").Range("A2:T" & o).value
    Sheets("各校300分以上统计").Activate
    Range("B5:K19").ClearContents
    
    Application.ScreenUpdating = False    '关闭系统提示
    
    ReDim brr(1 To UBound(arr), 1 To 2)
    s = 1
    For i = 1 To UBound(arr)
        If arr(i, 1) = Sheets("各校300分以上统计").Cells(1, 3).value Then
            brr(s, 1) = arr(i, 3)   '学校列
            '信息技术及格记10分，不及格记0分
            brr(s, 2) = arr(i, 18) '获取对应年级的学科:成绩
            s = s + 1
        End If
    Next
    If s = 1 Then
        MsgBox "数据不存在,请查看成绩总表！"
        Sheets("各校300分以上统计").Activate
        Exit Sub
        
    End If
        x = s - 1
        For k = 1 To x
            l = Application.Match(brr(k, 1), crr, 0)
            If brr(k, 2) >= 500 Then drr(l, 9) = drr(l, 9) + 1
            If brr(k, 2) >= 450 Then drr(l, 7) = drr(l, 7) + 1
            If brr(k, 2) >= 400 Then drr(l, 5) = drr(l, 5) + 1
            If brr(k, 2) >= 350 Then drr(l, 3) = drr(l, 3) + 1
            If brr(k, 2) >= 300 Then drr(l, 1) = drr(l, 1) + 1
        Next k
        Sheets("各校300分以上统计").Range("B5").Resize(15, 9) = drr
    Erase drr
End Sub
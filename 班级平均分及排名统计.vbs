Sub 统计班级平均分及排名()
    Dim wb As Workbook
    Dim ws成绩 As Worksheet, ws教师 As Worksheet, ws统计 As Worksheet
    Dim lastRow As Long, i <PERSON>, outputRow As Long
    Dim dict班级 As Object, dict教师 As Object
    Dim arr成绩(), arr教师(), arr输出()
    Dim startTime As Double
    Dim j As Integer
    Dim classKeys() As Variant
    Dim allGrades As Object
    Dim currentGrade As Variant
    Dim gradeKeys As Variant
    Dim currentOutputRow As Long

    ' 记录开始时间
    startTime = Timer
    
    ' 初始化字典对象
    Set allGrades = CreateObject("Scripting.Dictionary")
    
    ' 设置工作表对象
    Set wb = ThisWorkbook
    On Error Resume Next
    Set ws成绩 = wb.Sheets("成绩汇总表")
    Set ws教师 = wb.Sheets("任课教师")
    Set ws统计 = wb.Sheets("各年级各班统计")
    
    ' 创建或获取统计工作表
    If ws统计 Is Nothing Then
        Set ws统计 = wb.Sheets.Add(After:=wb.Sheets(wb.Sheets.Count))
        ws统计.Name = "各年级各班统计"
    Else
'        ws统计.Cells.Clear
    End If
    On Error GoTo 0
    
    ' 检查工作表是否存在
    If ws成绩 Is Nothing Then
        MsgBox "找不到工作表:成绩汇总表", vbExclamation
        Exit Sub
    End If
    If ws教师 Is Nothing Then
        MsgBox "找不到工作表:任课教师", vbExclamation
        Exit Sub
    End If
    If ws统计 Is Nothing Then
        MsgBox "找不到工作表:班级", vbExclamation
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' 设置统计表标题
    ws统计.Range("A1").Value = "年级各班统计表"
    With ws统计.Range("A1")
        .Font.Size = 14
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
    End With
    ws统计.Range("A1:W1").Merge
    
    ' 从成绩表中获取所有年级
    lastRow = ws成绩.Cells(ws成绩.Rows.Count, 1).End(xlUp).Row
    For i = 2 To lastRow
        currentGrade = Trim(ws成绩.Cells(i, 4).Value)
        If currentGrade <> "" And Not allGrades.Exists(currentGrade) Then
            allGrades.Add currentGrade, currentGrade
        End If
    Next i
    
    ' 检查是否有年级数据
    If allGrades.Count = 0 Then
        MsgBox "未找到任何年级数据", vbExclamation
        Exit Sub
    End If
    
    ' 获取所有年级
    gradeKeys = allGrades.Keys
    
    ' 初始化输出行计数器，从表头下一行开始
    currentOutputRow = 4
    
    ' 为每个年级创建统计
    For Each currentGrade In gradeKeys
        ' 初始化字典对象
        Set dict班级 = CreateObject("Scripting.Dictionary")
        Set dict教师 = CreateObject("Scripting.Dictionary")
        
        ' 1. 读取任课教师数据
        lastRow = ws教师.Cells(ws教师.Rows.Count, 1).End(xlUp).Row
        If lastRow > 1 Then
            arr教师 = ws教师.Range("A1:R" & lastRow).Value
            
            For i = 2 To UBound(arr教师, 1)
                If Trim(arr教师(i, 3)) = currentGrade Then
                    Dim teacherKey As String
                    teacherKey = arr教师(i, 1) & "|" & arr教师(i, 2) & "|" & arr教师(i, 4)
                    
                    If Not dict教师.Exists(teacherKey) Then
                        Dim teacherInfo(1 To 5) As String
                        teacherInfo(1) = arr教师(i, 6)   '语文教师
                        teacherInfo(2) = arr教师(i, 7)   '数学教师
                        teacherInfo(3) = arr教师(i, 8)   '英语教师
                        teacherInfo(4) = arr教师(i, 9)   '科学教师
                        teacherInfo(5) = arr教师(i, 10)  '道法教师
                        
                        dict教师.Add teacherKey, teacherInfo
                    End If
                End If
            Next i
        End If
        
        ' 2. 统计成绩数据
        lastRow = ws成绩.Cells(ws成绩.Rows.Count, 1).End(xlUp).Row
        If lastRow < 2 Then
            MsgBox "成绩汇总表没有数据", vbInformation
            GoTo NextGrade
        End If
        
        arr成绩 = ws成绩.Range("A1:X" & lastRow).Value
        
        For i = 2 To UBound(arr成绩, 1)
            If Trim(arr成绩(i, 4)) = currentGrade Then
                Dim classKey As String
                classKey = arr成绩(i, 2) & "|" & arr成绩(i, 3) & "|" & arr成绩(i, 5)
                
                If Not dict班级.Exists(classKey) Then
                    Dim classInfo(1 To 14) As Variant
                    ' 初始化统计项
                    classInfo(1) = arr成绩(i, 2)   '学校
                    classInfo(2) = arr成绩(i, 3)   '校点
                    classInfo(3) = arr成绩(i, 5)   '班级
                    classInfo(4) = 0               '参考人数
                    classInfo(5) = 0               '语文总分
                    classInfo(6) = 0               '数学总分
                    classInfo(7) = 0               '英语总分
                    classInfo(8) = 0               '科学总分
                    classInfo(9) = 0               '道法总分
                    classInfo(10) = 0              '语文参考人数
                    classInfo(11) = 0              '数学参考人数
                    classInfo(12) = 0              '英语参考人数
                    classInfo(13) = 0              '科学参考人数
                    classInfo(14) = 0              '道法参考人数
                    
                    dict班级.Add classKey, classInfo
                End If
                
                ' 统计各科成绩
                If dict班级.Exists(classKey) Then
                    Dim classData As Variant
                    classData = dict班级(classKey)
                    
                    ' 统计参考人数(有总分的人数)
                    If Val(arr成绩(i, 24)) > 0 Then
                        classData(4) = classData(4) + 1
                        dict班级(classKey) = classData
                    End If
                    
                    ' 统计各科成绩
                    If Val(arr成绩(i, 9)) > 0 Then  '语文
                        classData(5) = classData(5) + Val(arr成绩(i, 9))
                        classData(10) = classData(10) + 1
                    End If
                    
                    If Val(arr成绩(i, 10)) > 0 Then '数学
                        classData(6) = classData(6) + Val(arr成绩(i, 10))
                        classData(11) = classData(11) + 1
                    End If
                    
                    If Val(arr成绩(i, 11)) > 0 Then '英语
                        classData(7) = classData(7) + Val(arr成绩(i, 11))
                        classData(12) = classData(12) + 1
                    End If
                    
                    If Val(arr成绩(i, 12)) > 0 Then '科学
                        classData(8) = classData(8) + Val(arr成绩(i, 12))
                        classData(13) = classData(13) + 1
                    End If
                    
                    If Val(arr成绩(i, 13)) > 0 Then '道法
                        classData(9) = classData(9) + Val(arr成绩(i, 13))
                        classData(14) = classData(14) + 1
                    End If
                    
                    dict班级(classKey) = classData
                End If
            End If
        Next i
        
        ' 如果该年级没有班级数据，则跳过
        If dict班级.Count = 0 Then
            GoTo NextGrade
        End If
        
        ' 获取班级键数组
        classKeys = dict班级.Keys
        
        ' 3. 计算全区统计数据
        ' 全区统计数组结构:1-学校,2-校点,3-班级,4-参考人数,5-语文总分,6-数学总分,7-英语总分,8-科学总分,9-道法总分,
        ' 10-语文参考人数,11-数学参考人数,12-英语参考人数,13-科学参考人数,14-道法参考人数
        Dim arr全区合计(1 To 14) As Variant
        For i = 0 To UBound(classKeys)
            If dict班级.Exists(classKeys(i)) Then
                classData = dict班级(classKeys(i))
                ' 育英学校参与全区统计但不参与排名
                For j = 4 To 14  ' 统计所有相关数据项
                    arr全区合计(j) = arr全区合计(j) + classData(j)
                Next
            End If
        Next i
    
        ReDim arr输出(1 To dict班级.Count + 1, 1 To 24) ' 增加一行给全区统计，增加一列给年级
        Dim classIndex As Long
        classIndex = 0
    
        ' 添加全区统计行(显示在第一行)
        classIndex = classIndex + 1
        arr输出(classIndex, 1) = ""     '序号
        arr输出(classIndex, 2) = currentGrade  '年级
        arr输出(classIndex, 3) = ""     '学校
        arr输出(classIndex, 4) = "全区"  '校点
        arr输出(classIndex, 5) = "合计：" '班级
        arr输出(classIndex, 6) = arr全区合计(4) '全区参考人数
        
        ' 计算各科平均分(使用各科实际参考人数)
        arr输出(classIndex, 7) = Round(arr全区合计(5) / IIf(arr全区合计(10) > 0, arr全区合计(10), 1), 2) '语文平均
        arr输出(classIndex, 10) = Round(arr全区合计(6) / IIf(arr全区合计(11) > 0, arr全区合计(11), 1), 2) '数学平均
        arr输出(classIndex, 13) = Round(arr全区合计(7) / IIf(arr全区合计(12) > 0, arr全区合计(12), 1), 2) '英语平均
        arr输出(classIndex, 16) = Round(arr全区合计(8) / IIf(arr全区合计(13) > 0, arr全区合计(13), 1), 2) '科学平均
        arr输出(classIndex, 19) = Round(arr全区合计(9) / IIf(arr全区合计(14) > 0, arr全区合计(14), 1), 2) '道法平均
        
        ' 计算总分平均(使用各科都参考的学生人数)
        Dim totalScore As Double
        totalScore = arr全区合计(5) + arr全区合计(6) + arr全区合计(7) + arr全区合计(8) + arr全区合计(9)
        Dim totalCount As Long
        totalCount = Application.WorksheetFunction.Min(arr全区合计(10), arr全区合计(11), arr全区合计(12), arr全区合计(13), arr全区合计(14))
        arr输出(classIndex, 22) = Round(totalScore / IIf(totalCount > 0, totalCount, 1), 2) '总分平均
        
        ' 标记为合计行不参与排名
        arr输出(classIndex, 24) = "合计"
    
        ' 计算平均分
        classKeys = dict班级.Keys
        
        ' 按班级顺序输出(包含育英学校但不参与排名)
        For i = 0 To UBound(classKeys)
            classIndex = classIndex + 1
            If dict班级.Exists(classKeys(i)) Then
                classData = dict班级(classKeys(i))
                
                ' 标记育英学校不参与排名
                If InStr(classKeys(i), "育英学校") > 0 Then
                    arr输出(classIndex, 24) = "不排名"
                End If
                    
                ' 基本信息
                arr输出(classIndex, 1) = classIndex - 1          '序号
                arr输出(classIndex, 2) = currentGrade            '年级
                arr输出(classIndex, 3) = classData(1)            '学校
                arr输出(classIndex, 4) = classData(2)            '校点
                arr输出(classIndex, 5) = classData(3)            '班级
                arr输出(classIndex, 6) = classData(4)            '参考人数
                
                                ' 计算各科平均分
                arr输出(classIndex, 7) = Round(classData(5) / IIf(classData(10) > 0, classData(10), 1), 2)  '语文平均分
                arr输出(classIndex, 10) = Round(classData(6) / IIf(classData(11) > 0, classData(11), 1), 2)  '数学平均分
                arr输出(classIndex, 13) = Round(classData(7) / IIf(classData(12) > 0, classData(12), 1), 2) '英语平均分
                arr输出(classIndex, 16) = Round(classData(8) / IIf(classData(13) > 0, classData(13), 1), 2) '科学平均分
                arr输出(classIndex, 19) = Round(classData(9) / IIf(classData(14) > 0, classData(14), 1), 2) '道法平均分
                
                ' 计算总分平均
                Dim classTotal As Double
                classTotal = classData(5) + classData(6) + classData(7) + classData(8) + classData(9)
                Dim classCount As Long
                classCount = Application.WorksheetFunction.Min(classData(10), classData(11), classData(12), classData(13), classData(14))
                arr输出(classIndex, 22) = Round(classTotal / IIf(classCount > 0, classCount, 1), 2) '总分平均
                
                ' 添加任课教师信息
                Dim teacherInfoKey As String
                teacherInfoKey = classData(1) & "|" & classData(2) & "|" & classData(3)
                
                If dict教师.Exists(teacherInfoKey) Then
                    Dim teacherData As Variant
                    teacherData = dict教师(teacherInfoKey)
                    
                    arr输出(classIndex, 9) = teacherData(1)  '语文教师
                    arr输出(classIndex, 12) = teacherData(2) '数学教师
                    arr输出(classIndex, 15) = teacherData(3) '英语教师
                    arr输出(classIndex, 18) = teacherData(4) '科学教师
                    arr输出(classIndex, 21) = teacherData(5) '道法教师
                End If
            End If
        Next i
        
        ' 4. 计算排名
        ' 语文排名
        Call 计算排名(arr输出, 7, 8, "合计", "不排名")
        ' 数学排名
        Call 计算排名(arr输出, 10, 11, "合计", "不排名")
        ' 英语排名
        Call 计算排名(arr输出, 13, 14, "合计", "不排名")
        ' 科学排名
        Call 计算排名(arr输出, 16, 17, "合计", "不排名")
        ' 道法排名
        Call 计算排名(arr输出, 19, 20, "合计", "不排名")
        ' 总分排名
        Call 计算排名(arr输出, 22, 23, "合计", "不排名")
        
        ' 5. 输出到统计工作表
        ws统计.Range("A" & currentOutputRow).Resize(UBound(arr输出, 1), 23).Value = arr输出
        
        ' 设置全区行格式（第一行）
        With ws统计.Range("A" & currentOutputRow & ":W" & currentOutputRow)
            .Interior.Color = RGB(255, 255, 0) ' 黄色背景
            .Font.Bold = True                  ' 粗体
        End With
        
        ' 设置表格边框
        With ws统计.Range("A" & currentOutputRow & ":W" & (currentOutputRow + UBound(arr输出, 1) - 1)).Borders
            .LineStyle = 1    ' xlContinuous
            .Weight = 2       ' xlThin
        End With
        
        ' 更新当前输出行，加上当前年级的行数，再加1行空行
        currentOutputRow = currentOutputRow + UBound(arr输出, 1) + 1
        
NextGrade:
    Next currentGrade
    
    ' 设置列宽
    ws统计.Columns("A:W").AutoFit
    
    ' 恢复Excel设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    
    ' 显示完成消息
    MsgBox "已完成所有年级的统计，共处理 " & allGrades.Count & " 个年级，耗时: " & Round(Timer - startTime, 2) & "秒", vbInformation
    
    Exit Sub
    
CleanUp:
    ' 错误处理和清理
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "处理过程中出现错误", vbCritical
End Sub

' 计算排名的辅助函数
Sub 计算排名(ByRef arr() As Variant, ByVal scoreCol As Integer, ByVal rankCol As Integer, ByVal skipTag1 As String, ByVal skipTag2 As String)
    Dim i As Long, j As Long
    Dim rowCount As Long
    
    rowCount = UBound(arr, 1)
    
    ' 计算排名
    For i = 1 To rowCount
        ' 跳过不参与排名的行
        If arr(i, 24) = skipTag1 Or arr(i, 24) = skipTag2 Then
            arr(i, rankCol) = "-"
            GoTo NextRow
        End If
        
        Dim rank As Long
        rank = 1
        
        For j = 1 To rowCount
            ' 跳过不参与排名的行
            If arr(j, 24) = skipTag1 Or arr(j, 24) = skipTag2 Then
                GoTo NextCompare
            End If
            
            ' 如果其他行的分数更高，则当前行的排名+1
            If arr(j, scoreCol) > arr(i, scoreCol) Then
                rank = rank + 1
            End If
            
NextCompare:
        Next j
        
        arr(i, rankCol) = rank
        
NextRow:
    Next i
End Sub

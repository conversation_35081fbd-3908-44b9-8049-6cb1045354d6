// 游戏常量
const GRID_SIZE = 20;
const CELL_SIZE = 20;
const DIRECTIONS = {
    UP: { x: 0, y: -1 },
    DOWN: { x: 0, y: 1 },
    LEFT: { x: -1, y: 0 },
    RIGHT: { x: 1, y: 0 }
};

// 游戏状态
let snake = [
    { x: 10, y: 10 },
    { x: 9, y: 10 },
    { x: 8, y: 10 }
];
let food = generateFood();
let direction = DIRECTIONS.RIGHT;
let nextDirection = DIRECTIONS.RIGHT;
let score = 0;
let gameSpeed = 150;
let gameLoop;

// 获取Canvas元素和上下文
const canvas = document.getElementById('game-canvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');

// 初始化游戏
function initGame() {
    document.addEventListener('keydown', changeDirection);
    gameLoop = setInterval(updateGame, gameSpeed);
}

// 更新游戏状态
function updateGame() {
    direction = nextDirection;
    
    // 移动蛇
    const head = {
        x: snake[0].x + direction.x,
        y: snake[0].y + direction.y
    };
    
    // 检查碰撞
    if (checkCollision(head)) {
        gameOver();
        return;
    }
    
    snake.unshift(head);
    
    // 检查是否吃到食物
    if (head.x === food.x && head.y === food.y) {
        score += 10;
        scoreElement.textContent = `得分: ${score}`;
        food = generateFood();
    } else {
        snake.pop();
    }
    
    // 绘制游戏
    drawGame();
}

// 绘制游戏
function drawGame() {
    // 清空画布
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制蛇
    ctx.fillStyle = '#4CAF50';
    snake.forEach(segment => {
        ctx.fillRect(
            segment.x * CELL_SIZE, 
            segment.y * CELL_SIZE, 
            CELL_SIZE, 
            CELL_SIZE
        );
    });
    
    // 绘制食物
    ctx.fillStyle = '#F44336';
    ctx.fillRect(
        food.x * CELL_SIZE, 
        food.y * CELL_SIZE, 
        CELL_SIZE, 
        CELL_SIZE
    );
}

// 生成食物
function generateFood() {
    let food;
    do {
        food = {
            x: Math.floor(Math.random() * GRID_SIZE),
            y: Math.floor(Math.random() * GRID_SIZE)
        };
    } while (snake.some(segment => segment.x === food.x && segment.y === food.y));
    
    return food;
}

// 改变方向
function changeDirection(e) {
    switch(e.key) {
        case 'ArrowUp':
            if (direction !== DIRECTIONS.DOWN) nextDirection = DIRECTIONS.UP;
            break;
        case 'ArrowDown':
            if (direction !== DIRECTIONS.UP) nextDirection = DIRECTIONS.DOWN;
            break;
        case 'ArrowLeft':
            if (direction !== DIRECTIONS.RIGHT) nextDirection = DIRECTIONS.LEFT;
            break;
        case 'ArrowRight':
            if (direction !== DIRECTIONS.LEFT) nextDirection = DIRECTIONS.RIGHT;
            break;
    }
}

// 检查碰撞
function checkCollision(head) {
    return (
        head.x < 0 || 
        head.x >= GRID_SIZE || 
        head.y < 0 || 
        head.y >= GRID_SIZE || 
        snake.some(segment => segment.x === head.x && segment.y === head.y)
    );
}

// 游戏结束
function gameOver() {
    clearInterval(gameLoop);
    alert(`游戏结束! 你的得分是: ${score}`);
    
    // 重置游戏
    snake = [
        { x: 10, y: 10 },
        { x: 9, y: 10 },
        { x: 8, y: 10 }
    ];
    food = generateFood();
    direction = DIRECTIONS.RIGHT;
    nextDirection = DIRECTIONS.RIGHT;
    score = 0;
    scoreElement.textContent = `得分: 0`;
    gameLoop = setInterval(updateGame, gameSpeed);
}

// 启动游戏
initGame();
' 成绩计算.vbs
' 此文件包含与成绩总表中分数计算相关的功能

Sub CalculateScores()
    ' Calculate total scores and weighted scores in "成绩总表"
    Dim ws As Worksheet
    Dim lastRow As Long, lastCol As Long
    Dim i <PERSON>, j <PERSON> Long
    Dim totalScore As Double
    Dim weightedScore As Double
    Dim dataArray As Variant
    Dim resultsArray As Variant

    ' Get the "成绩总表" worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("成绩总表")
    On Error GoTo 0

    If ws Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find the last row and column with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

    ' Clear data in columns Q to T starting from row 2
    If lastRow >= 2 Then
        ws.Range(ws.Cells(2, 17), ws.Cells(lastRow, 20)).ClearContents ' Columns Q(17) to T(20)
    End If

    ' Find column indices for subjects and scores
    Dim colYuwen As Integer, colShuxue As Integer, colYingyu As Integer
    Dim colDaofa As Integer, colShengwu As Integer, colDili As Integer
    Dim colXinxi As Integer, colLishi As Integer, colWuli As Integer
    Dim colHuaxue As Integer, colTotal As Integer, colWeighted As Integer
    Dim colClassRank As Integer, colGradeRank As Integer
    Dim colGrade As Integer, colSchoolId As Integer, colSchool As Integer, colClass As Integer
    Dim colName As Integer ' 姓名列

    colYuwen = 0: colShuxue = 0: colYingyu = 0: colDaofa = 0: colShengwu = 0
    colDili = 0: colXinxi = 0: colLishi = 0: colWuli = 0: colHuaxue = 0
    colTotal = 0: colWeighted = 0: colClassRank = 0: colGradeRank = 0
    colGrade = 0: colSchoolId = 0: colSchool = 0: colClass = 0: colName = 0

    ' Find column indices (assuming headers are in row 1)
    For i = 1 To lastCol
        Select Case ws.Cells(1, i).value
            Case "年级": colGrade = i
            Case "学校序号": colSchoolId = i
            Case "学校": colSchool = i
            Case "班级": colClass = i
            Case "姓名": colName = i ' 姓名在第6列（F列）
            Case "语文": colYuwen = i
            Case "数学": colShuxue = i
            Case "英语": colYingyu = i
            Case "道法": colDaofa = i
            Case "生物": colShengwu = i
            Case "地理": colDili = i
            Case "信息": colXinxi = i
            Case "历史": colLishi = i
            Case "物理": colWuli = i
            Case "化学": colHuaxue = i
            Case "总分": colTotal = i ' 总分在第17列（Q列）
            Case "折分": colWeighted = i
            Case "班级排名": colClassRank = i
            Case "年级排名": colGradeRank = i
        End Select
    Next i

    ' Add ranking columns if they don't exist
    If colClassRank = 0 Then
        ' Find the next available column
        Dim nextCol As Integer
        nextCol = lastCol + 1

        ' Add "班级排名" column
        ws.Cells(1, nextCol).value = "班级排名"
        colClassRank = nextCol

        ' Add "年级排名" column
        ws.Cells(1, nextCol + 1).value = "年级排名"
        colGradeRank = nextCol + 1

        ' Update lastCol
        lastCol = nextCol + 1
    End If

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value

    ' Create a results array for total scores, weighted scores, and rankings
    ReDim resultsArray(1 To lastRow, 1 To 4) ' 4 columns: total, weighted, class rank, grade rank

    ' Process the data array - clean non-positive and non-numeric values in columns G to P
    For i = 2 To UBound(dataArray, 1) ' Start from row 2
        ' Check and clean subject scores (columns G to P)
        If colYuwen > 0 Then
            If Not IsNumeric(dataArray(i, colYuwen)) Or dataArray(i, colYuwen) <= 0 Then
                dataArray(i, colYuwen) = ""
            End If
        End If

        If colShuxue > 0 Then
            If Not IsNumeric(dataArray(i, colShuxue)) Or dataArray(i, colShuxue) <= 0 Then
                dataArray(i, colShuxue) = ""
            End If
        End If

        If colYingyu > 0 Then
            If Not IsNumeric(dataArray(i, colYingyu)) Or dataArray(i, colYingyu) <= 0 Then
                dataArray(i, colYingyu) = ""
            End If
        End If

        If colDaofa > 0 Then
            If Not IsNumeric(dataArray(i, colDaofa)) Or dataArray(i, colDaofa) <= 0 Then
                dataArray(i, colDaofa) = ""
            End If
        End If

        If colShengwu > 0 Then
            If Not IsNumeric(dataArray(i, colShengwu)) Or dataArray(i, colShengwu) <= 0 Then
                dataArray(i, colShengwu) = ""
            End If
        End If

        If colDili > 0 Then
            If Not IsNumeric(dataArray(i, colDili)) Or dataArray(i, colDili) <= 0 Then
                dataArray(i, colDili) = ""
            End If
        End If

        If colXinxi > 0 Then
            If Not IsNumeric(dataArray(i, colXinxi)) Or dataArray(i, colXinxi) <= 0 Then
                dataArray(i, colXinxi) = ""
            End If
        End If

        If colLishi > 0 Then
            If Not IsNumeric(dataArray(i, colLishi)) Or dataArray(i, colLishi) <= 0 Then
                dataArray(i, colLishi) = ""
            End If
        End If

        If colWuli > 0 Then
            If Not IsNumeric(dataArray(i, colWuli)) Or dataArray(i, colWuli) <= 0 Then
                dataArray(i, colWuli) = ""
            End If
        End If

        If colHuaxue > 0 Then
            If Not IsNumeric(dataArray(i, colHuaxue)) Or dataArray(i, colHuaxue) <= 0 Then
                dataArray(i, colHuaxue) = ""
            End If
        End If

        ' Calculate total score
        totalScore = 0
        If colYuwen > 0 And IsNumeric(dataArray(i, colYuwen)) Then totalScore = totalScore + dataArray(i, colYuwen)
        If colShuxue > 0 And IsNumeric(dataArray(i, colShuxue)) Then totalScore = totalScore + dataArray(i, colShuxue)
        If colYingyu > 0 And IsNumeric(dataArray(i, colYingyu)) Then totalScore = totalScore + dataArray(i, colYingyu)
        If colDaofa > 0 And IsNumeric(dataArray(i, colDaofa)) Then totalScore = totalScore + dataArray(i, colDaofa)
        If colShengwu > 0 And IsNumeric(dataArray(i, colShengwu)) Then totalScore = totalScore + dataArray(i, colShengwu)
        If colDili > 0 And IsNumeric(dataArray(i, colDili)) Then totalScore = totalScore + dataArray(i, colDili)
        If colXinxi > 0 And IsNumeric(dataArray(i, colXinxi)) Then totalScore = totalScore + dataArray(i, colXinxi)
        If colLishi > 0 And IsNumeric(dataArray(i, colLishi)) Then totalScore = totalScore + dataArray(i, colLishi)
        If colWuli > 0 And IsNumeric(dataArray(i, colWuli)) Then totalScore = totalScore + dataArray(i, colWuli)
        If colHuaxue > 0 And IsNumeric(dataArray(i, colHuaxue)) Then totalScore = totalScore + dataArray(i, colHuaxue)

        ' Calculate weighted score
        Dim yuwen As Double, shuxue As Double, yingyu As Double
        Dim daofa As Double, shengwu As Double, lishi As Double
        Dim dili As Double, huaxue As Double, wuli As Double

        yuwen = IIf(colYuwen > 0 And IsNumeric(dataArray(i, colYuwen)), dataArray(i, colYuwen), 0)
        shuxue = IIf(colShuxue > 0 And IsNumeric(dataArray(i, colShuxue)), dataArray(i, colShuxue), 0)
        yingyu = IIf(colYingyu > 0 And IsNumeric(dataArray(i, colYingyu)), dataArray(i, colYingyu), 0)
        daofa = IIf(colDaofa > 0 And IsNumeric(dataArray(i, colDaofa)), dataArray(i, colDaofa), 0)
        shengwu = IIf(colShengwu > 0 And IsNumeric(dataArray(i, colShengwu)), dataArray(i, colShengwu), 0)
        lishi = IIf(colLishi > 0 And IsNumeric(dataArray(i, colLishi)), dataArray(i, colLishi), 0)
        dili = IIf(colDili > 0 And IsNumeric(dataArray(i, colDili)), dataArray(i, colDili), 0)
        huaxue = IIf(colHuaxue > 0 And IsNumeric(dataArray(i, colHuaxue)), dataArray(i, colHuaxue), 0)
        wuli = IIf(colWuli > 0 And IsNumeric(dataArray(i, colWuli)), dataArray(i, colWuli), 0)

        weightedScore = yuwen + shuxue + yingyu + (daofa + shengwu + lishi) * 0.4 + (dili + huaxue) * 0.3 + wuli * 0.5

        ' Store results in the results array
        If totalScore > 0 Then
            resultsArray(i, 1) = totalScore ' Total score
            resultsArray(i, 2) = Round(weightedScore, 2) ' Weighted score
        Else
            resultsArray(i, 1) = "" ' Total score
            resultsArray(i, 2) = "" ' Weighted score
        End If
    Next i

    ' Write the cleaned data back to the worksheet
    ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value = dataArray

    ' Write total scores and weighted scores to the worksheet
    For i = 2 To lastRow
        If colTotal > 0 Then ws.Cells(i, colTotal).Value = resultsArray(i, 1)
        If colWeighted > 0 Then ws.Cells(i, colWeighted).Value = resultsArray(i, 2)
    Next i

    ' Calculate rankings after all scores are calculated
    Call CalculateRankings(ws, lastRow, colGrade, colSchoolId, colSchool, colClass, colName, colTotal, colWeighted, colClassRank, colGradeRank)
End Sub

Sub CalculateRankings(ws As Worksheet, lastRow As Long, colGrade As Integer, colSchoolId As Integer, colSchool As Integer, colClass As Integer, colName As Integer, colTotal As Integer, colWeighted As Integer, colClassRank As Integer, colGradeRank As Integer)
    ' Calculate class and grade rankings based on weighted scores
    Dim i As Long, j As Long
    Dim gradeDict As Object
    Dim classDict As Object
    Dim gradeClassKey As Variant
    Dim grade As Variant, schoolId As String, school As String, class As String, studentName As String
    Dim studentKey As Variant
    Dim dataArray As Variant
    Dim rankArray As Variant

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, colGradeRank)).Value

    ' Create a rank array to store class and grade rankings
    ReDim rankArray(1 To lastRow, 1 To 2) ' 2 columns: class rank, grade rank

    ' Create dictionaries to store grades, classes, and class statistics
    Set gradeDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")
    Set schoolDict = CreateObject("Scripting.Dictionary")

    ' Dictionaries to store class and grade statistics
    Dim classStatsDict As Object
    Dim gradeStatsDict As Object
    Set classStatsDict = CreateObject("Scripting.Dictionary")
    Set gradeStatsDict = CreateObject("Scripting.Dictionary")

    ' Dictionaries to track students by name for actual student count
    Dim classStudentDict As Object
    Dim gradeStudentDict As Object
    Set classStudentDict = CreateObject("Scripting.Dictionary")
    Set gradeStudentDict = CreateObject("Scripting.Dictionary")

    ' First pass: Count all students with names (实考人数)
    For i = 2 To UBound(dataArray, 1)
        grade = dataArray(i, colGrade)
        schoolId = dataArray(i, colSchoolId)
        school = dataArray(i, colSchool)
        class = dataArray(i, colClass)
        studentName = dataArray(i, colName)

        ' Create keys
        gradeClassKey = grade & "|" & schoolId & "|" & school & "|" & class

        ' Initialize dictionaries if they don't exist
        If Not classStatsDict.Exists(gradeClassKey) Then
            classStatsDict.Add gradeClassKey, Array(0, 0, 0) ' 实考人数, 参考人数, 总分
            Set classStudentDict(gradeClassKey) = CreateObject("Scripting.Dictionary")
        End If

        If Not gradeStatsDict.Exists(grade) Then
            gradeStatsDict.Add grade, Array(0, 0, 0) ' 实考人数, 参考人数, 总分
            Set gradeStudentDict(grade) = CreateObject("Scripting.Dictionary")
        End If

        ' Count actual students by name (实考人数)
        ' 注意：即使名字为空，也计入实考人数，因为每一行都代表一个学生
        Dim studentClassKey As String
        studentClassKey = i & "|" & gradeClassKey ' 使用行号作为学生的唯一标识

        If Not classStudentDict(gradeClassKey).Exists(studentClassKey) Then
            classStudentDict(gradeClassKey).Add studentClassKey, 1
            classStats = classStatsDict(gradeClassKey)
            classStats(0) = classStats(0) + 1 ' 实考人数 +1
            classStatsDict(gradeClassKey) = classStats
        End If

        Dim studentGradeKey As String
        studentGradeKey = i & "|" & grade ' 使用行号作为学生的唯一标识

        If Not gradeStudentDict(grade).Exists(studentGradeKey) Then
            gradeStudentDict(grade).Add studentGradeKey, 1
            gradeStats = gradeStatsDict(grade)
            gradeStats(0) = gradeStats(0) + 1 ' 实考人数 +1
            gradeStatsDict(grade) = gradeStats
        End If
    Next i

    ' Second pass: Count participating students and calculate scores
    For i = 2 To UBound(dataArray, 1)
        grade = dataArray(i, colGrade)
        schoolId = dataArray(i, colSchoolId)
        school = dataArray(i, colSchool)
        class = dataArray(i, colClass)

        ' Create keys
        gradeClassKey = grade & "|" & schoolId & "|" & school & "|" & class

        ' Skip if no total score (for ranking)
        If IsEmpty(dataArray(i, colTotal)) Or Not IsNumeric(dataArray(i, colTotal)) Then
            rankArray(i, 1) = "" ' Class rank
            rankArray(i, 2) = "" ' Grade rank
            GoTo NextStudent
        End If

        ' Add grade to dictionary if not exists (for ranking)
        If Not gradeDict.Exists(grade) Then
            gradeDict.Add grade, CreateObject("Scripting.Dictionary")
        End If

        ' Add class to dictionary if not exists (for ranking)
        If Not classDict.Exists(gradeClassKey) Then
            classDict.Add gradeClassKey, CreateObject("Scripting.Dictionary")
        End If

        ' Add student to grade and class dictionaries (for ranking)
        Dim totalScore As Double
        totalScore = dataArray(i, colTotal)

        ' Add to grade dictionary with row number as key
        gradeDict(grade).Add i, totalScore

        ' Add to class dictionary with row number as key
        classDict(gradeClassKey).Add i, totalScore

        ' Update statistics for total score (参考人数 and 总分)
        classStats = classStatsDict(gradeClassKey)
        classStats(1) = classStats(1) + 1 ' 参考人数 +1
        classStats(2) = classStats(2) + totalScore ' 总分 += 总分
        classStatsDict(gradeClassKey) = classStats

        gradeStats = gradeStatsDict(grade)
        gradeStats(1) = gradeStats(1) + 1 ' 参考人数 +1
        gradeStats(2) = gradeStats(2) + totalScore ' 总分 += 总分
        gradeStatsDict(grade) = gradeStats

NextStudent:
    Next i

    ' Verify that 实考人数 >= 参考人数 for all classes and grades
    Dim hasError As Boolean
    hasError = False

    For Each gradeClassKey In classStatsDict.Keys
        classStats = classStatsDict(gradeClassKey)
        If classStats(0) < classStats(1) Then
            ' 实考人数 should be >= 参考人数
            Debug.Print "Error: Class " & gradeClassKey & " has 实考人数 (" & classStats(0) & ") < 参考人数 (" & classStats(1) & ")"
            hasError = True

            ' Fix the issue by setting 参考人数 = 实考人数
            classStats(1) = classStats(0)
            classStatsDict(gradeClassKey) = classStats
        End If
    Next gradeClassKey

    For Each grade In gradeStatsDict.Keys
        gradeStats = gradeStatsDict(grade)
        If gradeStats(0) < gradeStats(1) Then
            ' 实考人数 should be >= 参考人数
            Debug.Print "Error: Grade " & grade & " has 实考人数 (" & gradeStats(0) & ") < 参考人数 (" & gradeStats(1) & ")"
            hasError = True

            ' Fix the issue by setting 参考人数 = 实考人数
            gradeStats(1) = gradeStats(0)
            gradeStatsDict(grade) = gradeStats
        End If
    Next grade

    If hasError Then
        MsgBox "警告：存在实考人数小于参考人数的情况，已自动修正。请检查数据！", vbExclamation, "数据验证"
    End If

    ' Calculate class rankings
    Dim classStudents As Object
    Dim classScores() As Double
    Dim classRows() As Long
    Dim classCount As Long
    Dim currentRank As Long
    Dim tempScore As Double
    Dim tempRow As Long

    ' Calculate class average scores and store in a dictionary
    Dim classAvgDict As Object
    Set classAvgDict = CreateObject("Scripting.Dictionary")

    For Each gradeClassKey In classStatsDict.Keys
        classStats = classStatsDict(gradeClassKey)
        If classStats(1) > 0 Then ' 如果有参考人数
            classAvgDict(gradeClassKey) = classStats(2) / classStats(1) ' 平均分 = 总分 / 参考人数
        Else
            classAvgDict(gradeClassKey) = 0
        End If
    Next gradeClassKey

    ' Calculate grade average scores and store in a dictionary
    Dim gradeAvgDict As Object
    Set gradeAvgDict = CreateObject("Scripting.Dictionary")

    For Each grade In gradeStatsDict.Keys
        gradeStats = gradeStatsDict(grade)
        If gradeStats(1) > 0 Then ' 如果有参考人数
            gradeAvgDict(grade) = gradeStats(2) / gradeStats(1) ' 平均分 = 总分 / 参考人数
        Else
            gradeAvgDict(grade) = 0
        End If
    Next grade

    ' Calculate class rankings based on total scores
    For Each gradeClassKey In classDict.Keys
        ' Get all students in this class
        Set classStudents = classDict(gradeClassKey)

        ' Sort students by total score (descending)
        classCount = classStudents.Count
        ReDim classScores(1 To classCount)
        ReDim classRows(1 To classCount)

        j = 1
        For Each studentKey In classStudents.Keys
            classRows(j) = studentKey
            classScores(j) = classStudents(studentKey)
            j = j + 1
        Next studentKey

        ' Sort using bubble sort (descending)
        For i = 1 To classCount - 1
            For j = i + 1 To classCount
                If classScores(i) < classScores(j) Then
                    ' Swap scores
                    tempScore = classScores(i)
                    classScores(i) = classScores(j)
                    classScores(j) = tempScore

                    ' Swap rows
                    tempRow = classRows(i)
                    classRows(i) = classRows(j)
                    classRows(j) = tempRow
                End If
            Next j
        Next i

        ' Assign class ranks with proper handling of ties
        currentRank = 1

        ' Assign first rank
        rankArray(classRows(1), 1) = currentRank

        ' Assign remaining ranks with tie handling
        For i = 2 To classCount
            If classScores(i) < classScores(i - 1) Then
                ' Different score, increment rank
                currentRank = i
            End If
            ' Assign the rank (same rank for tied scores)
            rankArray(classRows(i), 1) = currentRank
        Next i
    Next gradeClassKey

    ' Calculate grade rankings based on total scores
    Dim gradeStudents As Object
    Dim gradeScores() As Double
    Dim gradeRows() As Long
    Dim gradeCount As Long
    Dim currentGradeRank As Long
    Dim tempGradeScore As Double
    Dim tempGradeRow As Long

    For Each grade In gradeDict.Keys
        ' Get all students in this grade
        Set gradeStudents = gradeDict(grade)

        ' Sort students by total score (descending)
        gradeCount = gradeStudents.Count
        ReDim gradeScores(1 To gradeCount)
        ReDim gradeRows(1 To gradeCount)

        j = 1
        For Each studentKey In gradeStudents.Keys
            gradeRows(j) = studentKey
            gradeScores(j) = gradeStudents(studentKey)
            j = j + 1
        Next studentKey

        ' Sort using bubble sort (descending)
        For i = 1 To gradeCount - 1
            For j = i + 1 To gradeCount
                If gradeScores(i) < gradeScores(j) Then
                    ' Swap scores
                    tempGradeScore = gradeScores(i)
                    gradeScores(i) = gradeScores(j)
                    gradeScores(j) = tempGradeScore

                    ' Swap rows
                    tempGradeRow = gradeRows(i)
                    gradeRows(i) = gradeRows(j)
                    gradeRows(j) = tempGradeRow
                End If
            Next j
        Next i

        ' Assign grade ranks with proper handling of ties
        currentGradeRank = 1

        ' Assign first rank
        rankArray(gradeRows(1), 2) = currentGradeRank

        ' Assign remaining ranks with tie handling
        For i = 2 To gradeCount
            If gradeScores(i) < gradeScores(i - 1) Then
                ' Different score, increment rank
                currentGradeRank = i
            End If
            ' Assign the rank (same rank for tied scores)
            rankArray(gradeRows(i), 2) = currentGradeRank
        Next i
    Next grade

    ' Write rankings back to the worksheet
    For i = 2 To lastRow
        ws.Cells(i, colClassRank).Value = rankArray(i, 1)
        ws.Cells(i, colGradeRank).Value = rankArray(i, 2)
    Next i

    ' Debug output for verification
    Debug.Print "Statistics Summary:"
    Debug.Print "Class Statistics:"
    For Each gradeClassKey In classStatsDict.Keys
        classStats = classStatsDict(gradeClassKey)
        Debug.Print "  " & gradeClassKey & ": 实考人数=" & classStats(0) & ", 参考人数=" & classStats(1) & ", 总分=" & classStats(2) & ", 平均分=" & (classStats(2) / IIf(classStats(1) > 0, classStats(1), 1))
    Next gradeClassKey

    Debug.Print "Grade Statistics:"
    For Each grade In gradeStatsDict.Keys
        gradeStats = gradeStatsDict(grade)
        Debug.Print "  " & grade & ": 实考人数=" & gradeStats(0) & ", 参考人数=" & gradeStats(1) & ", 总分=" & gradeStats(2) & ", 平均分=" & (gradeStats(2) / IIf(gradeStats(1) > 0, gradeStats(1), 1))
    Next grade
End Sub
Sub 计算总分折分()
    ' Main procedure to orchestrate the total score and weighted score calculation
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    On Error GoTo ErrorHandler

    ' Call the CalculateScores function from 成绩计算新.vbs
    CalculateScores

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True

    MsgBox "总分和折分计算完成，班级和年级排名已生成！", vbInformation, "成功"
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    MsgBox "处理过程中发生错误: " & Err.Description, vbCritical, "错误"
End Sub

' CalculateScores 函数已移至 成绩计算新.vbs 文件中

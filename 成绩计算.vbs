Sub 生成班级表1()
    Dim s, x, y, z As Integer

    Dim arr(), arr1(), brr() As Variant
    Dim dir1, dir2, dir3 As String
    Dim brr1(1 To 80, 1 To 12)  '一个学校不至于超过100个人
    
    x = Sheets("课程表").Range("C65536").End(xlUp).Row
    y = Sheets("成绩总表").Range("C65536").End(xlUp).Row
    arr = Sheets("课程表").Range("A2:M" & x).value
    brr = Sheets("成绩总表").Range("A2:P" & y).value
    
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    dir1 = Dir("D:\成绩汇总", vbDirectory)   '判断路径为"D:\成绩汇总"的文件夹是否存在
    If dir1 = "" Then                        '如果不存在，就新建一个
        MkDir ("D:\成绩汇总")
    End If
    dir2 = Dir("D:\成绩汇总" & "\" & Sheets("课程表").Range("S5") & Sheets("课程表").Range("S6"), vbDirectory) '判断路径为"D:\成绩汇总"的文件夹是否存在
    If dir2 = "" Then                        '如果不存在，就新建一个
        MkDir ("D:\成绩汇总" & "\" & Sheets("课程表").Range("S5") & Sheets("课程表").Range("S6"))
    End If
    dir2 = "D:\成绩汇总" & "\" & Sheets("课程表").Range("S5") & Sheets("课程表").Range("S6")
        
            For j = 1 To x - 1                          '循环课程表
                If arr(j, 1) = Sheets("课程表").Range("S1") Then
                    Sheets("成绩").Range("A1") = arr(j, 1) & " " & arr(j, 2) & "班 " & Sheets("课程表").Range("S5") & Sheets("课程表").Range("S6") & " " & Sheets("课程表").Range("S2") & " 成绩"
                    Sheets("成绩").Range("D2") = arr(j, 3)
                    Sheets("成绩").Range("H2") = arr(j, 2)
                    Sheets("统计").Range("D2") = Sheets("课程表").Range("S1")
                    arr1 = Sheets("课程表").Range("D" & j + 1 & ":" & "N" & j + 1).value
                    s = 1
                    For k = 1 To y - 1                      '循环查找相应学校成绩表中的数据
                        If arr(j, 1) = brr(k, 3) Then       '如果学校相同
                            dir3 = Dir(dir2 & "\" & arr(j, 1), vbDirectory)
                            If dir3 = "" Then
                                MkDir (dir2 & "\" & arr(j, 1))
                            End If
                            dir3 = dir2 & "\" & arr(j, 1)
                            If arr(j, 2) = brr(k, 4) Then   '如果班级也相同
    
    
                                For l = 5 To 16             '查找到的同校、同班级的数据给数组brr1
                                    brr1(s, l - 4) = brr(k, l)
                                Next l
                                s = s + 1                   '获取一行数据后先增加一行，进行下行数据判断。
                            End If
                        End If
                     Next k
                    Sheets("成绩").Range("A5:L100").ClearContents
                    Sheets("成绩").Range("A5").Resize(80, 12) = brr1
                    Sheets("成绩").Range("C3").Resize(, 10) = arr1
                    Erase brr1
                    Sheets(Array("成绩", "统计")).Copy
                    Sheets("成绩").Select
                    Application.CutCopyMode = False
                    ActiveWorkbook.SaveAs Filename:=dir3 & "\" & Sheets("成绩").Range("H2") & ".xlsx"
    
                        Application.DisplayAlerts = False  '不出现对话框
                    ActiveWindow.Close
                End If
            Next j
       mOpen = Shell("Explorer.exe " & dir3, vbNormalFocus)
            Application.ScreenUpdating = True
End Sub


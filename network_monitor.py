"""
Network speed monitoring functionality.
"""
import time
import logging
import threading
import psutil

logger = logging.getLogger("定时关机")

class NetworkMonitor:
    """Monitor network speed and detect when it's below a threshold."""
    
    def __init__(self, threshold_kbps, duration_seconds, callback=None):
        """Initialize the network monitor.
        
        Args:
            threshold_kbps: The network speed threshold in KB/s.
            duration_seconds: The duration in seconds that the network speed must be below the threshold.
            callback: A function to call when the network speed is below the threshold for the specified duration.
        """
        self.threshold_kbps = threshold_kbps
        self.duration_seconds = duration_seconds
        self.callback = callback
        self.monitor_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
    
    def start(self):
        """Start monitoring network speed."""
        if self.is_running:
            return False
        
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_thread, daemon=True)
        self.monitor_thread.start()
        self.is_running = True
        
        logger.info(f"Started network monitoring (threshold: {self.threshold_kbps} KB/s, duration: {self.duration_seconds}s)")
        return True
    
    def stop(self):
        """Stop monitoring network speed."""
        if not self.is_running:
            return False
        
        self.stop_event.set()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.is_running = False
        
        logger.info("Stopped network monitoring")
        return True
    
    def _monitor_thread(self):
        """Thread function to monitor network speed."""
        start_time = None
        
        while not self.stop_event.is_set():
            try:
                network_speed = self._get_network_speed()
                
                if network_speed < self.threshold_kbps:
                    if start_time is None:
                        start_time = time.time()
                        logger.debug(f"Network speed below threshold: {network_speed:.2f} KB/s")
                    
                    elapsed = time.time() - start_time
                    if elapsed >= self.duration_seconds:
                        logger.info(f"Network speed below threshold for {self.duration_seconds}s")
                        if self.callback:
                            self.callback()
                        break
                else:
                    start_time = None
                
                time.sleep(1.0)
            except Exception as e:
                logger.error(f"Error monitoring network speed: {e}")
                time.sleep(5.0)  # Wait a bit longer on error
    
    def _get_network_speed(self):
        """Get the current network speed in KB/s."""
        net_io_before = psutil.net_io_counters()
        time.sleep(1)
        net_io_after = psutil.net_io_counters()
        
        # Calculate bytes sent and received in the last second
        bytes_sent = net_io_after.bytes_sent - net_io_before.bytes_sent
        bytes_recv = net_io_after.bytes_recv - net_io_before.bytes_recv
        
        # Convert to KB/s
        kb_sent = bytes_sent / 1024
        kb_recv = bytes_recv / 1024
        
        return kb_sent + kb_recv  # Total network speed
    
    def get_current_speed(self):
        """Get the current network speed in KB/s."""
        return self._get_network_speed()

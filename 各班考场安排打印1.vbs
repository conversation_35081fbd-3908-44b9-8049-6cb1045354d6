Sub 各班考场打印()
' 初始化变量

Dim ws As Worksheet
Dim lastRow As Long
Dim currentSchool As String
Dim currentGrade As String
Dim currentClass As String
Dim roomNumber As Integer
Dim seatNumber As Integer
Dim i As Integer
Dim j As Integer
Dim sheet As Worksheet

' 设置工作表
Set ws = ThisWorkbook.Sheets("学生信息汇总表")
lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    For Each sheet In ThisWorkbook.Sheets
        If sheet.Name = "考场安排打印" Then
            Application.DisplayAlerts = False
            sheet.Delete
            Application.DisplayAlerts = True
        End If
    Next sheet
' 初始化学生字典（按校点、年级、班级分组）
Dim studentDict As Object
Set studentDict = CreateObject("Scripting.Dictionary")

' 遍历每一行，为每个校点、每个年级、每个班级的学生分配编号
For i = 2 To lastRow
    currentSchool = ws.Cells(i, "A").Value
    currentGrade = ws.Cells(i, "B").Value
    currentClass = ws.Cells(i, "C").Value

    ' 生成校点、年级和班级的组合键
    Dim dictKey As String
    dictKey = currentSchool & "-" & currentGrade & "-" & currentClass

    ' 如果字典中不存在该键，则初始化
    If Not studentDict.Exists(dictKey) Then
        Set studentDict(dictKey) = CreateObject("Scripting.Dictionary")
    End If

    studentID = studentDict(dictKey).Count + 1
    ' 为学生分配编号
    studentDict(dictKey).Add studentID, Array(ws.Cells(i, "D").Value, ws.Cells(i, "E").Value, ws.Cells(i, "F").Value, ws.Cells(i, "G").Value)
Next i

' 遍历每个校点、年级和班级，打印考场安排
Dim outputWs As Worksheet
Set outputWs = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
outputWs.Name = "考场安排打印"

Dim rowIndex As Integer
rowIndex = 1

For Each schoolGradeClassKey In studentDict.Keys
    currentSchool = Split(schoolGradeClassKey, "-")(0)
    currentGrade = Split(schoolGradeClassKey, "-")(1)
    currentClass = Split(schoolGradeClassKey, "-")(2)

    ' 打印表头
    outputWs.Cells(rowIndex, 1).Value = currentSchool & "   " & currentGrade & "年级   " & currentClass & "班"
    outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 11)).Merge
    ' 设置表头样式
    With outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 11))
        .HorizontalAlignment = xlCenter
        .Font.Size = 16
    End With
    rowIndex = rowIndex + 2
    outputWs.Cells(rowIndex, 1).Value = "序号"
    outputWs.Cells(rowIndex, 2).Value = "准考号"
    outputWs.Cells(rowIndex, 3).Value = "姓名"
    outputWs.Cells(rowIndex, 4).Value = "考场"
    outputWs.Cells(rowIndex, 5).Value = "座位"
    outputWs.Cells(rowIndex, 6).Value = " "
    outputWs.Cells(rowIndex, 7).Value = "序号"
    outputWs.Cells(rowIndex, 8).Value = "准考号"
    outputWs.Cells(rowIndex, 9).Value = "姓名"
    outputWs.Cells(rowIndex, 10).Value = "考场"
    outputWs.Cells(rowIndex, 11).Value = "座位"
    outputWs.Rows(rowIndex).Font.Size = 14
    
    ' 为标题行添加虚线底边框
    outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 5)).Borders(xlEdgeBottom).LineStyle = xlContinuous
    outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 5)).Borders(xlEdgeBottom).Weight = xlThin
    outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 5)).Borders(xlEdgeBottom).Color = RGB(128, 128, 128)
    
    outputWs.Range(outputWs.Cells(rowIndex, 7), outputWs.Cells(rowIndex, 11)).Borders(xlEdgeBottom).LineStyle = xlContinuous
    outputWs.Range(outputWs.Cells(rowIndex, 7), outputWs.Cells(rowIndex, 11)).Borders(xlEdgeBottom).Weight = xlThin
    outputWs.Range(outputWs.Cells(rowIndex, 7), outputWs.Cells(rowIndex, 11)).Borders(xlEdgeBottom).Color = RGB(128, 128, 128)

    rowIndex = rowIndex + 1

    ' 打印表内容
    Dim studentCount As Integer
    studentCount = studentDict(schoolGradeClassKey).Count
    For j = 1 To studentCount Step 2
        outputWs.Cells(rowIndex, 1).Value = j
        outputWs.Cells(rowIndex, 2).Value = studentDict(schoolGradeClassKey)(j)(0)
        outputWs.Cells(rowIndex, 3).Value = studentDict(schoolGradeClassKey)(j)(1)
        outputWs.Cells(rowIndex, 4).Value = studentDict(schoolGradeClassKey)(j)(2)
        outputWs.Cells(rowIndex, 5).Value = studentDict(schoolGradeClassKey)(j)(3)

        If j + 1 <= studentCount Then
            outputWs.Cells(rowIndex, 7).Value = j + 1
            outputWs.Cells(rowIndex, 8).Value = studentDict(schoolGradeClassKey)(j + 1)(0)
            outputWs.Cells(rowIndex, 9).Value = studentDict(schoolGradeClassKey)(j + 1)(1)
            outputWs.Cells(rowIndex, 10).Value = studentDict(schoolGradeClassKey)(j + 1)(2)
            outputWs.Cells(rowIndex, 11).Value = studentDict(schoolGradeClassKey)(j + 1)(3)
        End If
        rowIndex = rowIndex + 1

        ' 为所有学生信息列添加虚线底边
        If j = studentCount Or j + 1 = studentCount Then
            ' 如果是本班最后一名学生，设置完整底边框
            ' 设置左侧区域边框
            With outputWs.Range(outputWs.Cells(rowIndex - 1, 1), outputWs.Cells(rowIndex - 1, 5)).Borders(xlEdgeBottom)
                .LineStyle = xlContinuous
                .Weight = xlThin
                .Color = RGB(128, 128, 128)
            End With
            ' 设置右侧区域边框
            With outputWs.Range(outputWs.Cells(rowIndex - 1, 7), outputWs.Cells(rowIndex - 1, 11)).Borders(xlEdgeBottom)
                .LineStyle = xlContinuous
                .Weight = xlThin
                .Color = RGB(128, 128, 128)
            End With
        Else
            ' 普通学生行底边框
            ' 设置左侧区域边框
            With outputWs.Range(outputWs.Cells(rowIndex - 1, 1), outputWs.Cells(rowIndex - 1, 5)).Borders(xlEdgeBottom)
                .LineStyle = xlContinuous
                .Weight = xlThin
                .Color = RGB(128, 128, 128)
            End With
            ' 设置右侧区域边框
            With outputWs.Range(outputWs.Cells(rowIndex - 1, 7), outputWs.Cells(rowIndex - 1, 11)).Borders(xlEdgeBottom)
                .LineStyle = xlContinuous
                .Weight = xlThin
                .Color = RGB(128, 128, 128)
            End With
        End If
    Next j
        ' 在每个班级后添加分页符
    outputWs.HPageBreaks.Add outputWs.Cells(rowIndex, 1)
    
    ' 确保下一页从新行开始
    outputWs.Cells(rowIndex, 1).Value = ""
    outputWs.Cells(rowIndex, 1).Borders(xlEdgeTop).LineStyle = xlNone
    
    ' 在每个班级后留空行
    rowIndex = rowIndex + 1
Next schoolGradeClassKey
    ' 统一设置所有单元格居中对齐
    outputWs.UsedRange.HorizontalAlignment = xlCenter
    
    ' 设置列宽
    outputWs.Columns("A:A").ColumnWidth = 6 '序号
    outputWs.Columns("B:B").ColumnWidth =9  '准考号
    outputWs.Columns("C:C").ColumnWidth = 8 '姓名
    outputWs.Columns("D:D").ColumnWidth = 6 '考场
    outputWs.Columns("E:E").ColumnWidth = 6 '座位
    outputWs.Columns("F:F").ColumnWidth = 2 '空白列
    outputWs.Columns("G:G").ColumnWidth = 6 '序号
    outputWs.Columns("H:H").ColumnWidth = 9 '准考号
    outputWs.Columns("I:I").ColumnWidth = 8 '姓名
    outputWs.Columns("J:J").ColumnWidth = 6 '考场
    outputWs.Columns("K:K").ColumnWidth = 6 '座位
    
    ' 设置页脚
    With outputWs.PageSetup
        .CenterFooter = "第&P页 共&N页"
        .FooterMargin = Application.InchesToPoints(0.5)
    End With
    ' 预览表格
    outputWs.PrintPreview
End Sub
    

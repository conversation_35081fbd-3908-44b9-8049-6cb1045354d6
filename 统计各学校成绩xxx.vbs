Option Explicit

Sub 统计学校成绩()
    ' --- 变量声明 ---
    Dim wb As Workbook
    Dim ws成绩汇总 As Worksheet, ws学校统计 As Worksheet
    Dim i As Long, lastRow As Long
    Dim 年级 As String
    Dim dict学校统计 As Object ' 用于存储各学校统计数据
    Dim startTime As Double
    Dim scoreDataArr As Variant
    Dim outputRow As Long
    Dim arr学校平均分() As Variant ' 用于存储各学校各科平均分以进行排名
    Dim 学校名称 As Variant ' 用于学校统计

    ' --- 常量定义 ---
    ' 成绩汇总表列索引
    Const COL_SUM_序号 As Integer = 1
    Const COL_SUM_学校 As Integer = 2
    Const COL_SUM_年级 As Integer = 4
    Const COL_SUM_语文 As Integer = 9
    Const COL_SUM_数学 As Integer = 10
    Const COL_SUM_英语 As Integer = 11
    Const COL_SUM_科学 As Integer = 12
    Const COL_SUM_道德与法治 As Integer = 13
    Const COL_SUM_总分五科 As Integer = 22
    
    ' 学校统计表列索引
    Const COL_STAT_序号 As Integer = 1
    Const COL_STAT_学校 As Integer = 2
    Const COL_STAT_参考人数 As Integer = 3
    Const COL_STAT_语文平均分 As Integer = 4
    Const COL_STAT_语文平均分排名 As Integer = 5
    Const COL_STAT_数学平均分 As Integer = 6
    Const COL_STAT_数学平均分排名 As Integer = 7
    Const COL_STAT_英语平均分 As Integer = 8
    Const COL_STAT_英语平均分排名 As Integer = 9
    Const COL_STAT_科学平均分 As Integer = 10
    Const COL_STAT_科学平均分排名 As Integer = 11
    Const COL_STAT_道法平均分 As Integer = 12
    Const COL_STAT_道法平均分排名 As Integer = 13
    Const COL_STAT_总分得分 As Integer = 14
    Const COL_STAT_总分排名 As Integer = 15
    Const COL_STAT_优秀人数 As Integer = 16
    Const COL_STAT_优秀比例 As Integer = 17
    Const COL_STAT_合格人数 As Integer = 18
    Const COL_STAT_合格比例 As Integer = 19
    Const COL_STAT_低分人数 As Integer = 20
    Const COL_STAT_低分比例 As Integer = 21
    Const COL_STAT_最高分 As Integer = 22
    Const COL_STAT_最低分 As Integer = 23
    
    ' 优秀和合格分数线
    Const 优秀分数线 As Double = 80 ' 80分以上为优秀
    Const 合格分数线 As Double = 60 ' 60分以上为合格
    Const 低分分数线 As Double = 40 ' 40分以下为低分
    
    ' --- 初始化 ---
    startTime = Timer
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False
    
    Set dict学校统计 = CreateObject("Scripting.Dictionary")
    Set wb = ThisWorkbook
    
    On Error GoTo ErrorHandler
    Set ws成绩汇总 = wb.Sheets("成绩汇总表")
    
    ' 检查是否存在学校统计表
    On Error Resume Next
    Set ws学校统计 = wb.Sheets("学校统计")
    If ws学校统计 Is Nothing Then
        MsgBox "未找到'学校统计'表，请确保该表存在。", vbExclamation
        GoTo CleanUp
    End If
    On Error GoTo ErrorHandler
    
    ' 1. 从学校统计表C1单元格获取目标年级
    年级 = Trim(ws学校统计.Range("C1").Value)
    If 年级 = "" Then
        MsgBox "学校统计表C1单元格中未找到年级信息，请在C1单元格中输入年级。", vbExclamation
        GoTo CleanUp
    End If
    
    ' 2. 清除学校统计表第5行开始的所有数据
    Dim lastDataRow As Long
    lastDataRow = ws学校统计.Cells(ws学校统计.Rows.Count, 1).End(xlUp).Row
    If lastDataRow >= 5 Then
        ws学校统计.Rows("5:" & lastDataRow).ClearContents
    End If

    ' 3. 统计成绩数据
    lastRow = ws成绩汇总.Cells(ws成绩汇总.Rows.Count, COL_SUM_序号).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "成绩汇总表没有数据。", vbInformation
        GoTo CleanUp
    End If
    
    ' 一次性读取所有数据到数组中以提高性能
    scoreDataArr = ws成绩汇总.Range(ws成绩汇总.Cells(1, 1), ws成绩汇总.Cells(lastRow, COL_SUM_总分五科)).Value2

    ' 遍历数据并按学校统计
    For i = 2 To UBound(scoreDataArr, 1)
        If Trim(CStr(scoreDataArr(i, COL_SUM_年级))) = 年级 Then
            学校名称 = Trim(CStr(scoreDataArr(i, COL_SUM_学校)))
            
            ' 初始化学校统计数据
            If Not dict学校统计.Exists(学校名称) Then
                On Error Resume Next
                Dim schoolStats As Object
                Set schoolStats = CreateObject("Scripting.Dictionary")
                If schoolStats Is Nothing Then
                    MsgBox "创建学校统计字典失败", vbExclamation
                    GoTo CleanUp
                End If
                
                With schoolStats
                    .Add "人数", 0
                    .Add "语文总分", 0#
                    .Add "数学总分", 0#
                    .Add "英语总分", 0#
                    .Add "科学总分", 0#
                    .Add "道法总分", 0#
                    .Add "总分（五科）合计", 0#
                    .Add "语文参考人数", 0
                    .Add "数学参考人数", 0
                    .Add "英语参考人数", 0
                    .Add "科学参考人数", 0
                    .Add "道法参考人数", 0
                    .Add "实考人数", 0 ' 有任何一科成绩的学生数
                    
                    ' 统计项
                    .Add "优秀人数", 0
                    .Add "合格人数", 0
                    .Add "低分人数", 0
                    .Add "语文最高分", 0
                    .Add "语文最低分", 100
                    .Add "数学最高分", 0
                    .Add "数学最低分", 100
                    .Add "英语最高分", 0
                    .Add "英语最低分", 100
                    .Add "科学最高分", 0
                    .Add "科学最低分", 100
                    .Add "道法最高分", 0
                    .Add "道法最低分", 100
                    .Add "总分最高分", 0
                    .Add "总分最低分", 500 ' 假设满分为500
                End With
                
                If Err.Number = 0 Then
                    dict学校统计.Add 学校名称, schoolStats
                Else
                    MsgBox "添加学校统计数据时出错: " & Err.Description, vbExclamation
                    GoTo CleanUp
                End If
                On Error GoTo ErrorHandler
            End If
            
            ' 更新学校统计数据
            With dict学校统计(学校名称)
                .Item("人数") = .Item("人数") + 1
                
                ' 确保分数是数字，如果不是则按0处理
                Dim 语文分数 As Double: 语文分数 = Val(scoreDataArr(i, COL_SUM_语文))
                Dim 数学分数 As Double: 数学分数 = Val(scoreDataArr(i, COL_SUM_数学))
                Dim 英语分数 As Double: 英语分数 = Val(scoreDataArr(i, COL_SUM_英语))
                Dim 科学分数 As Double: 科学分数 = Val(scoreDataArr(i, COL_SUM_科学))
                Dim 道法分数 As Double: 道法分数 = Val(scoreDataArr(i, COL_SUM_道德与法治))
                Dim 总分 As Double: 总分 = Val(scoreDataArr(i, COL_SUM_总分五科))
                
                ' 检查是否有任何一科有成绩，如果有则实考人数+1
                If 语文分数 > 0 Or 数学分数 > 0 Or 英语分数 > 0 Or 科学分数 > 0 Or 道法分数 > 0 Then
                    .Item("实考人数") = .Item("实考人数") + 1
                    
                    ' 判断是否为优秀、合格或低分
                    Dim 平均分 As Double
                    平均分 = 总分 / 5 ' 五科平均分
                    
                    If 平均分 >= 优秀分数线 Then
                        .Item("优秀人数") = .Item("优秀人数") + 1
                    End If
                    
                    If 平均分 >= 合格分数线 Then
                        .Item("合格人数") = .Item("合格人数") + 1
                    End If
                    
                    If 平均分 < 低分分数线 Then
                        .Item("低分人数") = .Item("低分人数") + 1
                    End If
                    
                    ' 更新最高分和最低分
                    If 语文分数 > 0 Then
                        .Item("语文最高分") = WorksheetFunction.Max(.Item("语文最高分"), 语文分数)
                        .Item("语文最低分") = WorksheetFunction.Min(.Item("语文最低分"), 语文分数)
                    End If
                    
                    If 数学分数 > 0 Then
                        .Item("数学最高分") = WorksheetFunction.Max(.Item("数学最高分"), 数学分数)
                        .Item("数学最低分") = WorksheetFunction.Min(.Item("数学最低分"), 数学分数)
                    End If
                    
                    If 英语分数 > 0 Then
                        .Item("英语最高分") = WorksheetFunction.Max(.Item("英语最高分"), 英语分数)
                        .Item("英语最低分") = WorksheetFunction.Min(.Item("英语最低分"), 英语分数)
                    End If
                    
                    If 科学分数 > 0 Then
                        .Item("科学最高分") = WorksheetFunction.Max(.Item("科学最高分"), 科学分数)
                        .Item("科学最低分") = WorksheetFunction.Min(.Item("科学最低分"), 科学分数)
                    End If
                    
                    If 道法分数 > 0 Then
                        .Item("道法最高分") = WorksheetFunction.Max(.Item("道法最高分"), 道法分数)
                        .Item("道法最低分") = WorksheetFunction.Min(.Item("道法最低分"), 道法分数)
                    End If
                    
                    If 总分 > 0 Then
                        .Item("总分最高分") = WorksheetFunction.Max(.Item("总分最高分"), 总分)
                        .Item("总分最低分") = WorksheetFunction.Min(.Item("总分最低分"), 总分)
                    End If
                End If
                
                ' 累加各科总分并计数
                .Item("语文总分") = .Item("语文总分") + 语文分数
                If 语文分数 > 0 Then .Item("语文参考人数") = .Item("语文参考人数") + 1
                
                .Item("数学总分") = .Item("数学总分") + 数学分数
                If 数学分数 > 0 Then .Item("数学参考人数") = .Item("数学参考人数") + 1
                
                .Item("英语总分") = .Item("英语总分") + 英语分数
                If 英语分数 > 0 Then .Item("英语参考人数") = .Item("英语参考人数") + 1
                
                .Item("科学总分") = .Item("科学总分") + 科学分数
                If 科学分数 > 0 Then .Item("科学参考人数") = .Item("科学参考人数") + 1
                
                .Item("道法总分") = .Item("道法总分") + 道法分数
                If 道法分数 > 0 Then .Item("道法参考人数") = .Item("道法参考人数") + 1
                
                .Item("总分（五科）合计") = .Item("总分（五科）合计") + 总分
            End With
        End If
    Next i
    
    If dict学校统计.Count = 0 Then
        MsgBox "在成绩汇总表中未找到年级 '" & 年级 & "' 的数据。", vbInformation
        GoTo CleanUp
    End If

    ' 4. 准备学校平均分数组用于排名
    ReDim arr学校平均分(1 To dict学校统计.Count, 1 To 7) ' 学校名称, 语, 数, 英, 科, 道, 总
    
    ' 5. 写入各学校统计数据 - 从第5行开始
    outputRow = 5 ' 数据从第5行开始
    Dim 学校序号 As Long: 学校序号 = 1
    Dim 学校索引 As Long: 学校索引 = 0
    
    For Each 学校名称 In dict学校统计.Keys
        学校索引 = 学校索引 + 1
        arr学校平均分(学校索引, 1) = 学校名称 ' 存储学校名称用于后续排名
       
        ' 写入基本信息
        ws学校统计.Cells(outputRow, COL_STAT_序号).Value = 学校序号
        ws学校统计.Cells(outputRow, COL_STAT_学校).Value = 学校名称
        
        With dict学校统计(学校名称)
            ' 参考人数
            ws学校统计.Cells(outputRow, COL_STAT_参考人数).Value = .Item("实考人数")
            
            ' 计算并写入各科平均分
            Dim 学校语平 As Double: 学校语平 = IIf(.Item("语文参考人数") > 0, Round(.Item("语文总分") / .Item("语文参考人数"), 2), 0)
            Dim 学校数平 As Double: 学校数平 = IIf(.Item("数学参考人数") > 0, Round(.Item("数学总分") / .Item("数学参考人数"), 2), 0)
            Dim 学校英平 As Double: 学校英平 = IIf(.Item("英语参考人数") > 0, Round(.Item("英语总分") / .Item("英语参考人数"), 2), 0)
            Dim 学校科平 As Double: 学校科平 = IIf(.Item("科学参考人数") > 0, Round(.Item("科学总分") / .Item("科学参考人数"), 2), 0)
            Dim 学校道平 As Double: 学校道平 = IIf(.Item("道法参考人数") > 0, Round(.Item("道法总分") / .Item("道法参考人数"), 2), 0)
            
            ' 总分平均分计算基于实考人数
            Dim 学校总平 As Double: 学校总平 = IIf(.Item("实考人数") > 0, Round(.Item("总分（五科）合计") / .Item("实考人数"), 2), 0)
            
            ' 存储平均分用于排名
            arr学校平均分(学校索引, 2) = 学校语平
            arr学校平均分(学校索引, 3) = 学校数平
            arr学校平均分(学校索引, 4) = 学校英平
            arr学校平均分(学校索引, 5) = 学校科平
            arr学校平均分(学校索引, 6) = 学校道平
            arr学校平均分(学校索引, 7) = 学校总平
            
            ' 写入各科平均分
            ws学校统计.Cells(outputRow, COL_STAT_语文平均分).Value = 学校语平
            ws学校统计.Cells(outputRow, COL_STAT_数学平均分).Value = 学校数平
            ws学校统计.Cells(outputRow, COL_STAT_英语平均分).Value = 学校英平
            ws学校统计.Cells(outputRow, COL_STAT_科学平均分).Value = 学校科平
            ws学校统计.Cells(outputRow, COL_STAT_道法平均分).Value = 学校道平
            
            ' 写入总分得分
            ws学校统计.Cells(outputRow, COL_STAT_总分得分).Value = 学校总平
            
            ' 写入优秀人数和比例
            ws学校统计.Cells(outputRow, COL_STAT_优秀人数).Value = .Item("优秀人数")
            
            Dim 优秀比例 As Double
            优秀比例 = IIf(.Item("实考人数") > 0, .Item("优秀人数") / .Item("实考人数"), 0)
            ws学校统计.Cells(outputRow, COL_STAT_优秀比例).Value = 优秀比例
            ws学校统计.Cells(outputRow, COL_STAT_优秀比例).NumberFormat = "0.00%"
            
            ' 写入合格人数和比例
            ws学校统计.Cells(outputRow, COL_STAT_合格人数).Value = .Item("合格人数")
            
            Dim 合格比例 As Double
            合格比例 = IIf(.Item("实考人数") > 0, .Item("合格人数") / .Item("实考人数"), 0)
            ws学校统计.Cells(outputRow, COL_STAT_合格比例).Value = 合格比例
            ws学校统计.Cells(outputRow, COL_STAT_合格比例).NumberFormat = "0.00%"
            
            ' 写入低分人数和比例
            ws学校统计.Cells(outputRow, COL_STAT_低分人数).Value = .Item("低分人数")
            
            Dim 低分比例 As Double
            低分比例 = IIf(.Item("实考人数") > 0, .Item("低分人数") / .Item("实考人数"), 0)
            ws学校统计.Cells(outputRow, COL_STAT_低分比例).Value = 低分比例
            ws学校统计.Cells(outputRow, COL_STAT_低分比例).NumberFormat = "0.00%"
            
            ' 写入最高分和最低分
            ws学校统计.Cells(outputRow, COL_STAT_最高分).Value = .Item("总分最高分")
            ws学校统计.Cells(outputRow, COL_STAT_最低分).Value = IIf(.Item("总分最低分") < 500, .Item("总分最低分"), 0)
            
            ' 设置数字格式
            ws学校统计.Cells(outputRow, COL_STAT_语文平均分).NumberFormat = "0.00"
            ws学校统计.Cells(outputRow, COL_STAT_数学平均分).NumberFormat = "0.00"
            ws学校统计.Cells(outputRow, COL_STAT_英语平均分).NumberFormat = "0.00"
            ws学校统计.Cells(outputRow, COL_STAT_科学平均分).NumberFormat = "0.00"
            ws学校统计.Cells(outputRow, COL_STAT_道法平均分).NumberFormat = "0.00"
            ws学校统计.Cells(outputRow, COL_STAT_总分得分).NumberFormat = "0.00"
        End With
        
        学校序号 = 学校序号 + 1
        outputRow = outputRow + 1
    Next 学校名称
    
    ' 6. 计算并写入学校排名
    Call 计算学校排名(ws学校统计, arr学校平均分, dict学校统计.Count)
    
    ' 7. 计算并写入全区统计（不参与排名）
    Dim 全区统计 As Object
    Set 全区统计 = CreateObject("Scripting.Dictionary")
    With 全区统计
        .Add "语文总分", 0#
        .Add "数学总分", 0#
        .Add "英语总分", 0#
        .Add "科学总分", 0#
        .Add "道法总分", 0#
        .Add "总分", 0#
        .Add "语文参考人数", 0
        .Add "数学参考人数", 0
        .Add "英语参考人数", 0
        .Add "科学参考人数", 0
        .Add "道法参考人数", 0
        .Add "实考人数", 0
        .Add "优秀人数", 0
        .Add "合格人数", 0
        .Add "低分人数", 0
        .Add "语文最高分", 0
        .Add "语文最低分", 100
        .Add "数学最高分", 0
        .Add "数学最低分", 100
        .Add "英语最高分", 0
        .Add "英语最低分", 100
        .Add "科学最高分", 0
        .Add "科学最低分", 100
        .Add "道法最高分", 0
        .Add "道法最低分", 100
        .Add "总分最高分", 0
        .Add "总分最低分", 500
    End With

    ' 计算全区统计数据
    For Each 学校名称 In dict学校统计.Keys
        With dict学校统计(学校名称)
            全区统计("语文总分") = 全区统计("语文总分") + .Item("语文总分")
            全区统计("数学总分") = 全区统计("数学总分") + .Item("数学总分")
            全区统计("英语总分") = 全区统计("英语总分") + .Item("英语总分")
            全区统计("科学总分") = 全区统计("科学总分") + .Item("科学总分")
            全区统计("道法总分") = 全区统计("道法总分") + .Item("道法总分")
            全区统计("总分") = 全区统计("总分") + .Item("总分（五科）合计")
            全区统计("语文参考人数") = 全区统计("语文参考人数") + .Item("语文参考人数")
            全区统计("数学参考人数") = 全区统计("数学参考人数") + .Item("数学参考人数")
            全区统计("英语参考人数") = 全区统计("英语参考人数") + .Item("英语参考人数")
            全区统计("科学参考人数") = 全区统计("科学参考人数") + .Item("科学参考人数")
            全区统计("道法参考人数") = 全区统计("道法参考人数") + .Item("道法参考人数")
            全区统计("实考人数") = 全区统计("实考人数") + .Item("实考人数")
            全区统计("优秀人数") = 全区统计("优秀人数") + .Item("优秀人数")
            全区统计("合格人数") = 全区统计("合格人数") + .Item("合格人数")
            全区统计("低分人数") = 全区统计("低分人数") + .Item("低分人数")
            
            ' 更新全区最高分和最低分
            全区统计("语文最高分") = WorksheetFunction.Max(全区统计("语文最高分"), .Item("语文最高分"))
            全区统计("数学最高分") = WorksheetFunction.Max(全区统计("数学最高分"), .Item("数学最高分"))
            全区统计("英语最高分") = WorksheetFunction.Max(全区统计("英语最高分"), .Item("英语最高分"))
            全区统计("科学最高分") = WorksheetFunction.Max(全区统计("科学最高分"), .Item("科学最高分"))
            全区统计("道法最高分") = WorksheetFunction.Max(全区统计("道法最高分"), .Item("道法最高分"))
            全区统计("总分最高分") = WorksheetFunction.Max(全区统计("总分最高分"), .Item("总分最高分"))
            
            ' 只有当学校有实际数据时才更新最低分
            If .Item("语文最低分") < 100 Then 全区统计("语文最低分") = WorksheetFunction.Min(全区统计("语文最低分"), .Item("语文最低分"))
            If .Item("数学最低分") < 100 Then 全区统计("数学最低分") = WorksheetFunction.Min(全区统计("数学最低分"), .Item("数学最低分"))
            If .Item("英语最低分") < 100 Then 全区统计("英语最低分") = WorksheetFunction.Min(全区统计("英语最低分"), .Item("英语最低分"))
            If .Item("科学最低分") < 100 Then 全区统计("科学最低分") = WorksheetFunction.Min(全区统计("科学最低分"), .Item("科学最低分"))
            If .Item("道法最低分") < 100 Then 全区统计("道法最低分") = WorksheetFunction.Min(全区统计("道法最低分"), .Item("道法最低分"))
            If .Item("总分最低分") < 500 Then 全区统计("总分最低分") = WorksheetFunction.Min(全区统计("总分最低分"), .Item("总分最低分"))
        End With
    Next

    ' 写入全区统计数据
    Dim 全区行 As Long
    全区行 = outputRow + 1 ' 留一行空白
    ws学校统计.Cells(全区行, COL_STAT_序号).Value = ""
    ws学校统计.Cells(全区行, COL_STAT_学校).Value = "全区统计"
    
    ' 写入全区参考人数
    ws学校统计.Cells(全区行, COL_STAT_参考人数).Value = 全区统计("实考人数")
    
    ' 计算并写入全区各科平均分
    Dim 全区语平 As Double: 全区语平 = IIf(全区统计("语文参考人数") > 0, Round(全区统计("语文总分") / 全区统计("语文参考人数"), 2), 0)
    Dim 全区数平 As Double: 全区数平 = IIf(全区统计("数学参考人数") > 0, Round(全区统计("数学总分") / 全区统计("数学参考人数"), 2), 0)
    Dim 全区英平 As Double: 全区英平 = IIf(全区统计("英语参考人数") > 0, Round(全区统计("英语总分") / 全区统计("英语参考人数"), 2), 0)
    Dim 全区科平 As Double: 全区科平 = IIf(全区统计("科学参考人数") > 0, Round(全区统计("科学总分") / 全区统计("科学参考人数"), 2), 0)
    Dim 全区道平 As Double: 全区道平 = IIf(全区统计("道法参考人数") > 0, Round(全区统计("道法总分") / 全区统计("道法参考人数"), 2), 0)
    Dim 全区总平 As Double: 全区总平 = IIf(全区统计("实考人数") > 0, Round(全区统计("总分") / 全区统计("实考人数"), 2), 0)
    
    ws学校统计.Cells(全区行, COL_STAT_语文平均分).Value = 全区语平
    ws学校统计.Cells(全区行, COL_STAT_数学平均分).Value = 全区数平
    ws学校统计.Cells(全区行, COL_STAT_英语平均分).Value = 全区英平
    ws学校统计.Cells(全区行, COL_STAT_科学平均分).Value = 全区科平
    ws学校统计.Cells(全区行, COL_STAT_道法平均分).Value = 全区道平
    
    ' 写入全区总分得分
    ws学校统计.Cells(全区行, COL_STAT_总分得分).Value = 全区总平
    
    ' 写入全区优秀人数和比例
    ws学校统计.Cells(全区行, COL_STAT_优秀人数).Value = 全区统计("优秀人数")
    
    Dim 全区优秀比例 As Double
    全区优秀比例 = IIf(全区统计("实考人数") > 0, 全区统计("优秀人数") / 全区统计("实考人数"), 0)
    ws学校统计.Cells(全区行, COL_STAT_优秀比例).Value = 全区优秀比例
    ws学校统计.Cells(全区行, COL_STAT_优秀比例).NumberFormat = "0.00%"
    
    ' 写入全区合格人数和比例
    ws学校统计.Cells(全区行, COL_STAT_合格人数).Value = 全区统计("合格人数")
    
    Dim 全区合格比例 As Double
    全区合格比例 = IIf(全区统计("实考人数") > 0, 全区统计("合格人数") / 全区统计("实考人数"), 0)
    ws学校统计.Cells(全区行, COL_STAT_合格比例).Value = 全区合格比例
    ws学校统计.Cells(全区行, COL_STAT_合格比例).NumberFormat = "0.00%"
    
    ' 写入全区低分人数和比例
    ws学校统计.Cells(全区行, COL_STAT_低分人数).Value = 全区统计("低分人数")
    
    Dim 全区低分比例 As Double
    全区低分比例 = IIf(全区统计("实考人数") > 0, 全区统计("低分人数") / 全区统计("实考人数"), 0)
    ws学校统计.Cells(全区行, COL_STAT_低分比例).Value = 全区低分比例
    ws学校统计.Cells(全区行, COL_STAT_低分比例).NumberFormat = "0.00%"
    
    ' 写入全区最高分和最低分
    ws学校统计.Cells(全区行, COL_STAT_最高分).Value = 全区统计("总分最高分")
    ws学校统计.Cells(全区行, COL_STAT_最低分).Value = IIf(全区统计("总分最低分") < 500, 全区统计("总分最低分"), 0)
    
    ' 设置全区统计行的数字格式
    ws学校统计.Cells(全区行, COL_STAT_语文平均分).NumberFormat = "0.00"
    ws学校统计.Cells(全区行, COL_STAT_数学平均分).NumberFormat = "0.00"
    ws学校统计.Cells(全区行, COL_STAT_英语平均分).NumberFormat = "0.00"
    ws学校统计.Cells(全区行, COL_STAT_科学平均分).NumberFormat = "0.00"
    ws学校统计.Cells(全区行, COL_STAT_道法平均分).NumberFormat = "0.00"
    ws学校统计.Cells(全区行, COL_STAT_总分得分).NumberFormat = "0.00"
    
    ' 加粗全区统计行
    Dim lastCol As Long: lastCol = COL_STAT_最低分 ' 使用最后一列的索引
    ws学校统计.Range(ws学校统计.Cells(全区行, 1), ws学校统计.Cells(全区行, lastCol)).Font.Bold = True
    
    ' 设置全区统计上边框加粗
    Dim totalTopBorder As Range
    Set totalTopBorder = ws学校统计.Range(ws学校统计.Cells(全区行, 1), ws学校统计.Cells(全区行, lastCol))
    totalTopBorder.Borders(xlEdgeTop).Weight = xlMedium
    
    ' 美化表格
    Dim dataRange As Range
    Set dataRange = ws学校统计.Range(ws学校统计.Cells(5, 1), ws学校统计.Cells(全区行, lastCol))
    With dataRange
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

CleanUp:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    Set dict学校统计 = Nothing
    Set 全区统计 = Nothing
    Set wb = Nothing
    Set ws成绩汇总 = Nothing
    Set ws学校统计 = Nothing
    If Err.Number = 0 Then
        MsgBox "学校统计完成！耗时: " & Format(Timer - startTime, "0.00") & " 秒", vbInformation
    End If
    Exit Sub

ErrorHandler:
    MsgBox "运行时错误 " & Err.Number & ": " & Err.Description, vbCritical, "错误"
    Resume CleanUp
End Sub
' 计算学校排名的子过程
Sub 计算学校排名(ws As Worksheet, arrScores() As Variant, schoolCount As Long)
    ' arrScores: 1=学校名称, 2=语, 3=数, 4=英, 5=科, 6=道, 7=总
    
    ' 定义排名列索引
    Dim rankCols As Variant
    Dim i As Integer
    rankCols = Array(5, 7, 9, 11, 13, 15) ' 语文、数学、英语、科学、道法、总分排名列
    
    Dim j As Long, scoreColIndexInArray As Long
    
    For j = 0 To UBound(rankCols) ' 遍历每个需要排名的科目
        scoreColIndexInArray = j + 2 ' 分数在 arrScores 数组中的列索引 (从第2列开始)
        
        ' 创建一个临时数组用于排序: [原始行索引, 分数, 学校名称]
        Dim sortableArray() As Variant
        Dim validCount As Long: validCount = 0
        
        ' 先计算有效记录数(排除育英学校)
        For i = 1 To schoolCount
            Dim 学校名称 As String
            学校名称 = CStr(arrScores(i, 1))  ' 确保转换为字符串
            If InStr(学校名称, "育英学校") = 0 Then ' 排除育英学校
                validCount = validCount + 1
            End If
        Next i
        
        If validCount = 0 Then
            ' 如果没有有效学校，跳过此科目排名
            GoTo NextSubject
        End If
        
        ReDim sortableArray(1 To validCount, 1 To 3)
        Dim idx As Long: idx = 0
        
        For i = 1 To schoolCount
            学校名称 = CStr(arrScores(i, 1))
            If InStr(学校名称, "育英学校") = 0 Then ' 排除育英学校
                idx = idx + 1
                sortableArray(idx, 1) = i ' 存储原始行号
                sortableArray(idx, 2) = CDbl(arrScores(i, scoreColIndexInArray)) ' 存储分数
                sortableArray(idx, 3) = 学校名称 ' 存储学校名称
            End If
        Next i
        
        ' 使用快速排序对分数进行降序排序
        Call QuickSortDescending(sortableArray, 1, validCount, 2)
        
        ' 生成排名并写入工作表 (处理并列排名)
        Dim currentRank As Long, actualRank As Long
        currentRank = 1
        actualRank = 1
        
        ' 为第一个学校写入排名
        Dim firstSchoolRow As Long
        firstSchoolRow = FindSchoolRow(ws, CStr(sortableArray(1, 3)), 5)  ' 从第5行开始查找
        If firstSchoolRow > 0 Then
            ws.Cells(firstSchoolRow, rankCols(j)).Value = actualRank
        End If
        
        ' 从第二个元素开始比较
        For i = 2 To validCount
            currentRank = currentRank + 1
            
            ' 安全比较浮点数
            Dim diff As Double
            diff = Abs(CDbl(sortableArray(i, 2)) - CDbl(sortableArray(i - 1, 2)))
            
            If diff < 0.0001 Then ' 并列
                ' 不更新 actualRank
            Else
                actualRank = currentRank
            End If
            
            ' 找到对应学校的行并写入排名
            Dim schoolRow As Long
            schoolRow = FindSchoolRow(ws, CStr(sortableArray(i, 3)), 5)  ' 从第5行开始查找
            If schoolRow > 0 Then
                ws.Cells(schoolRow, rankCols(j)).Value = actualRank
            End If
        Next i
        
NextSubject:
    Next j
End Sub

' 快速排序算法 - 降序
Sub QuickSortDescending(arr() As Variant, low As Long, high As Long, colIndex As Long)
    Dim pivot As Double, temp1 As Variant, temp2 As Variant, temp3 As Variant
    Dim i As Long, j As Long
    
    If low < high Then
        ' 选择中间元素作为基准点以避免最坏情况
        Dim mid As Long
        mid = (low + high) \ 2
        
        ' 交换中间元素和第一个元素
        temp1 = arr(low, 1): arr(low, 1) = arr(mid, 1): arr(mid, 1) = temp1
        temp2 = arr(low, 2): arr(low, 2) = arr(mid, 2): arr(mid, 2) = temp2
        temp3 = arr(low, 3): arr(low, 3) = arr(mid, 3): arr(mid, 3) = temp3
        
        pivot = CDbl(arr(low, colIndex))
        i = low
        j = high
        
        Do While i < j
            ' 从右向左找到第一个大于基准的元素
            Do While j > i
                If CDbl(arr(j, colIndex)) > pivot Then Exit Do
                j = j - 1
            Loop
            
            ' 从左向右找到第一个小于基准的元素
            Do While i < j
                If CDbl(arr(i, colIndex)) < pivot Then Exit Do
                i = i + 1
            Loop
            
            ' 交换这两个元素
            If i < j Then
                temp1 = arr(i, 1): arr(i, 1) = arr(j, 1): arr(j, 1) = temp1
                temp2 = arr(i, 2): arr(i, 2) = arr(j, 2): arr(j, 2) = temp2
                temp3 = arr(i, 3): arr(i, 3) = arr(j, 3): arr(j, 3) = temp3
            End If
        Loop
        
        ' 将基准元素放到正确位置
        temp1 = arr(low, 1): arr(low, 1) = arr(j, 1): arr(j, 1) = temp1
        temp2 = arr(low, 2): arr(low, 2) = arr(j, 2): arr(j, 2) = temp2
        temp3 = arr(low, 3): arr(low, 3) = arr(j, 3): arr(j, 3) = temp3
        
      ' 递归排序基准左右两部分
        Call QuickSortDescending(arr, low, j - 1, colIndex)
        Call QuickSortDescending(arr, j + 1, high, colIndex)
    End If
End Sub

' 辅助函数：根据学校名称查找对应的行
Function FindSchoolRow(ws As Worksheet, schoolName As String, startRow As Long) As Long
    Dim i As Long
    Dim lastRow As Long
    
    lastRow = ws.Cells(ws.Rows.Count, 2).End(xlUp).Row
    
    ' 使用二分查找优化查找速度
    Dim low As Long, high As Long, mid As Long
    low = startRow
    high = lastRow
    
    ' 如果数据量小，直接线性查找
    If high - low < 20 Then
        For i = low To high
            If ws.Cells(i, 2).Value = schoolName Then
                FindSchoolRow = i
                Exit Function
            End If
        Next i
    Else
        ' 对于大数据量，先尝试直接定位
        On Error Resume Next
        Dim foundRange As Range
        Set foundRange = ws.Range(ws.Cells(startRow, 2), ws.Cells(lastRow, 2)).Find(What:=schoolName, LookIn:=xlValues, LookAt:=xlWhole)
        If Not foundRange Is Nothing Then
            FindSchoolRow = foundRange.Row
            Exit Function
        End If
        On Error GoTo 0
        
        ' 如果直接定位失败，再使用线性查找
        For i = startRow To lastRow
            If ws.Cells(i, 2).Value = schoolName Then
                FindSchoolRow = i
                Exit Function
            End If
        Next i
    End If
    
    FindSchoolRow = 0 ' 未找到
End Function

Sub 计算全区考场()
    ' 声明变量并指定类型
    Dim m <PERSON>, x <PERSON>, i <PERSON> Long
    Dim lastRow As Long
    Dim ws As Worksheet
    Dim wsReport As Worksheet
    Dim mc <PERSON>, r <PERSON> bj As String
    Dim y As Long
    
    ' 获取当前工作表
    Set ws = ActiveSheet
    Set wsReport = ThisWorkbook.Worksheets("报名汇总")
    
    ' 使用更安全的方式获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "E").End(xlUp).Row
    
    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    
    ' 随机数填充
    For i = 2 To lastRow
        ws.Cells(i, 8) = Rnd()
    Next i
    
    ' 设置初始值
    With wsReport
        .Range(.Cells(2, 10), .Cells(2, 12)).FormulaR1C1 = "1"
    End With
    
    ' 获取报名汇总表的最后一行
    x = wsReport.Cells(wsReport.Rows.Count, "C").End(xlUp).Row
    
    ' 学校数组
    Dim sh As Variant
    sh = Array("思茅一小思茅一小", "思茅二小思茅二小", "思茅三小思茅三小", "思茅四小校本部", "思茅四小新时代", _
    "思茅五小思茅五小", "思茅六小中心校", "思茅六小土桥", "思茅六小整碗", "思茅六小南岛河", "思茅六中小学部", _
    "思茅七小思茅七小", "倚象小学蚌弄", "倚象小学菠萝", "倚象小学大寨", "倚象小学竜竜", "倚象小学纳吉", _
    "倚象小学石膏箐", "倚象小学下寨", "倚象小学一零一", "倚象小学营盘山", "倚象小学永庆", "倚象小学鱼塘", _
    "倚象小学中心校", "六顺小学中心校", "云仙小学骂木", "云仙小学中心校", "龙潭小学中心校", _
    "思茅港小茨竹林", "思茅港小莲花塘", "思茅港小那澜", "思茅港小中心校", "育英学校育英学校", "博雅公学")
    
    ' 座位数组
    Dim sh1 As Variant
    sh1 = Array(30, 30, 30, 30, 30, 30, 25, 25, 25, 25, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25, 25, 25, 30, 30, 30, 30)
    
    ' 以"学校序号"为第一关键字、"考点"为第二关键字、"班级"为第三关键字为关键字排序
    With wsReport
        .Range("A1:Q" & x).Sort _
            Key1:=.Range("B2"), Order1:=xlAscending, _
            Key2:=.Range("D2"), Order2:=xlAscending, _
            Key3:=.Range("E2"), Order3:=xlAscending, _
            Header:=xlYes, OrderCustom:=1, MatchCase:=False, _
            Orientation:=xlTopToBottom, SortMethod:=xlPinYin
    End With
    
    ' 排序后，计算名次
    mc = 1 ' 名次初值
    r = 2  ' 行号
    
    ' 安全检查：确保至少有一行数据
    If r <= x Then
        bj = wsReport.Cells(r, 4).Value & wsReport.Cells(r, 5).Value
        
        ' 使用For循环替代While循环，更安全且可控
        For r = 2 To x
            ' 检查班级是否存在
            If wsReport.Cells(r, 5).Value = "" Then Exit For
            
            If (wsReport.Cells(r, 4).Value & wsReport.Cells(r, 5).Value <> bj) Then
                mc = 1
                bj = wsReport.Cells(r, 4).Value & wsReport.Cells(r, 5).Value
            End If
            
            wsReport.Cells(r, 9).Value = mc ' 给"考试排序"赋值
            mc = mc + 1
        Next r
    End If
    
    ' 以"序号"为第一关键字、"考点"为第二关键字、"考试排序"为第三关键字为关键字排序
    With wsReport
        .Range("A1:Q" & x).Sort _
            Key1:=.Range("B2"), Order1:=xlAscending, _
            Key2:=.Range("D2"), Order2:=xlAscending, _
            Key3:=.Range("I2"), Order3:=xlAscending, _
            Header:=xlYes, OrderCustom:=1, MatchCase:=False, _
            Orientation:=xlTopToBottom, SortMethod:=xlPinYin
    End With
    
    ' 计算考试号、座位号、考场号
    ' 使用错误处理来捕获可能的匹配错误
    On Error Resume Next
    
    For m = 2 To x
        ' 安全检查：确保数据存在
        If wsReport.Cells(m, 3).Value = "" Or wsReport.Cells(m, 4).Value = "" Then
            ' 跳过空行
            GoTo ContinueLoop
        End If
        
        ' 查找匹配的学校索引
        y = Application.Match(wsReport.Cells(m, 3).Value & wsReport.Cells(m, 4).Value, sh, 0) - 1
        
        ' 检查匹配是否成功
        If Err.Number <> 0 Then
            MsgBox "无法找到学校: " & wsReport.Cells(m, 3).Value & wsReport.Cells(m, 4).Value, vbExclamation
            Err.Clear
            GoTo ContinueLoop
        End If
        
        ' 检查索引是否在有效范围内
        If y < 0 Or y >= UBound(sh1) + 1 Then
            MsgBox "学校索引超出范围: " & y, vbExclamation
            GoTo ContinueLoop
        End If
        
        ' 第一个学生或新学校/考点的第一个学生
        If m = 2 Or (wsReport.Cells(m - 1, 4).Value & wsReport.Cells(m - 1, 3).Value <> wsReport.Cells(m, 4).Value & wsReport.Cells(m, 3).Value) Then
            wsReport.Cells(m, 10).Value = 1 ' 考试号
            wsReport.Cells(m, 11).Value = 1 ' 考场号
            wsReport.Cells(m, 12).Value = 1 ' 座位号
        Else
            ' 计算考试号
            wsReport.Cells(m, 10).Value = wsReport.Cells(m - 1, 10).Value + 1
            
            ' 计算考场号
            wsReport.Cells(m, 11).Value = Int((wsReport.Cells(m, 10).Value - 1) / sh1(y)) + 1
            
            ' 计算座位号
            If wsReport.Cells(m, 10).Value Mod sh1(y) <> 0 Then
                wsReport.Cells(m, 12).Value = wsReport.Cells(m, 10).Value Mod sh1(y)
            Else
                wsReport.Cells(m, 12).Value = sh1(y)
            End If
        End If
        
ContinueLoop:
    Next m
    
    ' 恢复错误处理
    On Error GoTo 0
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    
    MsgBox "考场计算完成！", vbInformation
End Sub

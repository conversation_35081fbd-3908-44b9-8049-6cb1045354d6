Option Explicit

' 设置Excel表格格式
Dim excelApp, workbook, worksheet
On Error Resume Next

' 获取已打开的Excel应用
Set excelApp = GetObject(, "Excel.Application")
If Err.Number <> 0 Then
    MsgBox "请先打开Excel文件", vbExclamation
    WScript.Quit
End If
On Error GoTo 0

' 获取活动工作表
Set workbook = excelApp.ActiveWorkbook
Set worksheet = workbook.ActiveSheet

' 设置全区行格式（第2行）
With worksheet.Rows(2)
    .Interior.Color = RGB(255, 255, 0) ' 黄色背景
    .Font.Bold = True                  ' 粗体
End With

' 设置表格边框（A4到V列最后一行）
Dim lastRow
lastRow = worksheet.Cells(worksheet.Rows.Count, "A").End(-4162).Row ' xlUp

With worksheet.Range("A4:V" & lastRow).Borders
    .LineStyle = 1    ' xlContinuous
    .Weight = 2       ' xlThin
End With

MsgBox "表格格式设置完成", vbInformation
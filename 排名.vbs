Sub 排名()
    ' Calculate total scores and weighted scores in "成绩总表"
    Dim ws As Worksheet
    Dim lastRow As Long, lastCol As Long
    Dim i <PERSON>, j <PERSON>, k <PERSON> Long
    Dim totalScore As Double
    Dim weightedScore As Double
    Dim dataArray As Variant
    Dim resultsArray As Variant
    Dim formatRange As Range

    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    On Error GoTo ErrorHandler

    ' Get the "成绩总表" worksheet
    Set ws = ThisWorkbook.Worksheets("成绩总表")

    If ws Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        GoTo CleanExit
    End If

    ' Find the last row and column with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

    If lastRow < 2 Then
        MsgBox "成绩总表中没有数据！", vbExclamation, "错误"
        GoTo CleanExit
    End If

    ' Clear data in columns Q to T starting from row 2
    ws.Range(ws.Cells(2, 17), ws.Cells(lastRow, 20)).ClearContents ' Columns Q(17) to T(20)

    ' Create arrays to store column indices and subject names
    Dim colIndices(17) As Integer  ' Array to store all column indices
    Dim subjectNames(10) As String  ' Array to store subject names
    Dim subjectCols(10) As Integer  ' Array to store subject column indices
    Dim subjectWeights(10) As Double  ' Array to store subject weights for weighted score
    Dim subjectCount As Integer

    ' Initialize arrays
    For i = 1 To UBound(colIndices)
        colIndices(i) = 0
    Next i

    ' Define subject names and weights in a single loop
    subjectCount = 10  ' Total number of subjects
    For i = 1 To subjectCount
        Select Case i
            Case 1: subjectNames(i) = "语文": subjectWeights(i) = 1
            Case 2: subjectNames(i) = "数学": subjectWeights(i) = 1
            Case 3: subjectNames(i) = "英语": subjectWeights(i) = 1
            Case 4: subjectNames(i) = "道法": subjectWeights(i) = 0.4
            Case 5: subjectNames(i) = "生物": subjectWeights(i) = 0.4
            Case 6: subjectNames(i) = "历史": subjectWeights(i) = 0.4
            Case 7: subjectNames(i) = "地理": subjectWeights(i) = 0.3
            Case 8: subjectNames(i) = "化学": subjectWeights(i) = 0.3
            Case 9: subjectNames(i) = "物理": subjectWeights(i) = 0.5
            Case 10: subjectNames(i) = "信息": subjectWeights(i) = 0  ' Special handling for 信息
        End Select
    Next i

    ' Get all header data at once
    Dim headerArray As Variant
    headerArray = ws.Range(ws.Cells(1, 1), ws.Cells(1, lastCol)).Value

    ' Find column indices from header array
    For i = 1 To UBound(headerArray, 2)
        Select Case headerArray(1, i)
            Case "年级": colIndices(1) = i
            Case "学校": colIndices(2) = i
            Case "班级": colIndices(3) = i
            Case "总分": colIndices(4) = i
            Case "折分": colIndices(5) = i
            Case "班级排名": colIndices(6) = i
            Case "年级排名": colIndices(7) = i
            Case Else
                ' Check if it's one of our subjects
                For j = 1 To subjectCount
                    If headerArray(1, i) = subjectNames(j) Then
                        subjectCols(j) = i
                        Exit For
                    End If
                Next j
        End Select
    Next i

    ' For easier reference
    Dim colGrade As Integer: colGrade = colIndices(1)
    Dim colSchool As Integer: colSchool = colIndices(2)
    Dim colClass As Integer: colClass = colIndices(3)
    Dim colTotal As Integer: colTotal = colIndices(4)
    Dim colWeighted As Integer: colWeighted = colIndices(5)
    Dim colClassRank As Integer: colClassRank = colIndices(6)
    Dim colGradeRank As Integer: colGradeRank = colIndices(7)

    ' Add ranking columns if they don't exist
    If colClassRank = 0 Then
        ' Find the next available column
        Dim nextCol As Integer
        nextCol = lastCol + 1

        ' Add "班级排名" column
        ws.Cells(1, nextCol).Value = "班级排名"
        colClassRank = nextCol
        colIndices(6) = nextCol

        ' Add "年级排名" column
        ws.Cells(1, nextCol + 1).Value = "年级排名"
        colGradeRank = nextCol + 1
        colIndices(7) = nextCol + 1

        ' Update lastCol
        lastCol = nextCol + 1
    End If

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value

    ' Create a results array for total scores, weighted scores, and rankings
    ReDim resultsArray(1 To lastRow, 1 To 4) ' 4 columns: total, weighted, class rank, grade rank

    ' Process the data array - clean non-positive and non-numeric values for all subjects
    For i = 2 To UBound(dataArray, 1) ' Start from row 2
        ' Create an array to store subject scores for this student
        Dim subjectScores(10) As Double

        ' Clean subject scores and get all subject scores in a single loop
        For j = 1 To subjectCount
            If subjectCols(j) > 0 Then
                If j = 10 Then  ' Special handling for 信息
                    ' Add 10 points if information score is 60 or above, otherwise 0 points
                    If IsNumeric(dataArray(i, subjectCols(j))) And dataArray(i, subjectCols(j)) >= 60 Then
                        subjectScores(j) = 10
                    Else
                        subjectScores(j) = 0
                        If IsNumeric(dataArray(i, subjectCols(j))) And dataArray(i, subjectCols(j)) <= 0 Then
                            dataArray(i, subjectCols(j)) = ""
                        End If
                    End If
                Else
                    ' Regular subjects
                    If IsNumeric(dataArray(i, subjectCols(j))) And dataArray(i, subjectCols(j)) > 0 Then
                        subjectScores(j) = dataArray(i, subjectCols(j))
                    Else
                        subjectScores(j) = 0
                        dataArray(i, subjectCols(j)) = ""
                    End If
                End If
            End If
        Next j

        ' Calculate total score and weighted score in a single loop
        totalScore = 0
        weightedScore = 0

        For j = 1 To subjectCount
            If subjectScores(j) > 0 Then
                ' Add to total score (except for 信息 which is already calculated as points)
                If j < 10 Then totalScore = totalScore + subjectScores(j)

                ' Add to weighted score
                weightedScore = weightedScore + (subjectScores(j) * subjectWeights(j))
            End If
        Next j

        ' Store results in the results array
        If totalScore > 0 Then
            resultsArray(i, 1) = totalScore ' Total score
            resultsArray(i, 2) = Round(weightedScore, 2) ' Weighted score
        Else
            resultsArray(i, 1) = "" ' Total score
            resultsArray(i, 2) = "" ' Weighted score
        End If
    Next i

    ' Write the cleaned data back to the worksheet
    ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value = dataArray

    ' Write total scores and weighted scores to the worksheet in a single operation
    Dim scoreRange As Range
    Set scoreRange = ws.Range(ws.Cells(2, colTotal), ws.Cells(lastRow, colTotal))
    scoreRange.Value = Application.Index(resultsArray, 0, 1)

    Set scoreRange = ws.Range(ws.Cells(2, colWeighted), ws.Cells(lastRow, colWeighted))
    scoreRange.Value = Application.Index(resultsArray, 0, 2)

    ' Calculate rankings after all scores are calculated
    Call CalculateRankings(ws, lastRow, colGrade, colSchool, colClass, colWeighted, colClassRank, colGradeRank)

    ' Format number cells
    Set formatRange = ws.Range("G2:R" & lastRow)
    formatRange.NumberFormat = "0.00"

CleanExit:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    Exit Sub

ErrorHandler:
    MsgBox "处理过程中发生错误: " & Err.Description, vbCritical, "错误"
    Resume CleanExit
End Sub

Sub CalculateRankings(ws As Worksheet, lastRow As Long, colGrade As Integer, colSchool As Integer, colClass As Integer, colWeighted As Integer, colClassRank As Integer, colGradeRank As Integer)
    ' Calculate class and grade rankings based on weighted scores
    Dim i As Long, j As Long
    Dim gradeDict As Object
    Dim classDict As Object
    Dim gradeClassKey As String
    Dim grade As Variant, school As String, class As String
    Dim dataArray As Variant
    Dim rankArray As Variant

    On Error GoTo ErrorHandler

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, colGradeRank)).Value

    ' Create a rank array to store class and grade rankings
    ReDim rankArray(1 To lastRow, 1 To 2) ' 2 columns: class rank, grade rank

    ' Create dictionaries to store student data by grade and class
    Set gradeDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")

    ' Single pass: collect grades, classes and student scores
    For i = 2 To UBound(dataArray, 1)
        ' Get student data
        grade = dataArray(i, colGrade)
        If IsEmpty(grade) Then GoTo NextStudent

        school = dataArray(i, colSchool)
        class = dataArray(i, colClass)

        ' Skip if no weighted score
        If IsEmpty(dataArray(i, colWeighted)) Or Not IsNumeric(dataArray(i, colWeighted)) Then
            rankArray(i, 1) = "" ' Class rank
            rankArray(i, 2) = "" ' Grade rank
            GoTo NextStudent
        End If

        ' Create grade-class key
        gradeClassKey = CStr(grade) & "|" & school & "|" & class

        ' Add grade dictionary if it doesn't exist
        If Not gradeDict.Exists(CStr(grade)) Then
            gradeDict.Add CStr(grade), CreateObject("Scripting.Dictionary")
        End If

        ' Add class dictionary if it doesn't exist
        If Not classDict.Exists(gradeClassKey) Then
            classDict.Add gradeClassKey, CreateObject("Scripting.Dictionary")
        End If

        ' Add student to dictionaries with row number as key
        gradeDict(CStr(grade)).Add CStr(i), CDbl(dataArray(i, colWeighted))
        classDict(gradeClassKey).Add CStr(i), CDbl(dataArray(i, colWeighted))

NextStudent:
    Next i

    ' Process rankings
    Call ProcessRankings(classDict, rankArray, 1) ' Process class rankings (column 1)
    Call ProcessRankings(gradeDict, rankArray, 2) ' Process grade rankings (column 2)

    ' Write rankings back to the worksheet in a single operation
    Dim rankRange As Range
    Set rankRange = ws.Range(ws.Cells(2, colClassRank), ws.Cells(lastRow, colClassRank))
    rankRange.Value = Application.Index(rankArray, 0, 1)

    Set rankRange = ws.Range(ws.Cells(2, colGradeRank), ws.Cells(lastRow, colGradeRank))
    rankRange.Value = Application.Index(rankArray, 0, 2)

    Exit Sub

ErrorHandler:
    MsgBox "计算排名时发生错误: " & Err.Description, vbCritical, "错误"
    Resume Next
End Sub

' Helper function to process rankings for both class and grade
Sub ProcessRankings(dict As Object, rankArray As Variant, rankColumn As Integer)
    Dim groupKey As Variant
    Dim students As Object
    Dim studentKey As Variant
    Dim scores() As Double
    Dim rows() As Long
    Dim count As Long
    Dim i As Long, j As Long
    Dim currentRank As Long

    On Error Resume Next

    For Each groupKey In dict.Keys
        ' Get all students in this group
        Set students = dict(groupKey)

        ' Get count of students
        count = students.Count

        ' Skip if no students
        If count = 0 Then GoTo NextGroup

        ' Create arrays for scores and rows
        ReDim scores(1 To count)
        ReDim rows(1 To count)

        ' Fill arrays
        j = 1
        For Each studentKey In students.Keys
            rows(j) = CLng(studentKey)
            scores(j) = students(studentKey)
            j = j + 1
        Next studentKey

        ' Sort arrays using QuickSort
        Call QuickSort(scores, rows, 1, count)

        ' Assign ranks with proper handling of ties
        currentRank = 1

        ' Assign first rank
        rankArray(rows(1), rankColumn) = currentRank

        ' Assign remaining ranks with tie handling
        For i = 2 To count
            If scores(i) < scores(i - 1) Then
                ' Different score, increment rank
                currentRank = i
            End If
            ' Assign the rank (same rank for tied scores)
            rankArray(rows(i), rankColumn) = currentRank
        Next i

NextGroup:
    Next groupKey

    On Error GoTo 0
End Sub

' Optimized QuickSort implementation for sorting scores and rows
Sub QuickSort(scores() As Double, rows() As Long, low As Long, high As Long)
    Dim pivotStack(1 To 100, 1 To 2) As Long
    Dim stackPos As Long
    Dim pivot As Double
    Dim tempScore As Double
    Dim tempRow As Long
    Dim i As Long, j As Long
    Dim leftSize As Long, rightSize As Long

    ' Initialize stack
    stackPos = 1
    pivotStack(stackPos, 1) = low
    pivotStack(stackPos, 2) = high

    ' Non-recursive implementation using a stack
    Do While stackPos > 0
        ' Pop from stack
        low = pivotStack(stackPos, 1)
        high = pivotStack(stackPos, 2)
        stackPos = stackPos - 1

        ' Partition the array
        i = low
        j = high
        pivot = scores((low + high) \ 2)

        Do
            ' Find element on left that should be on right
            Do While scores(i) > pivot
                i = i + 1
                If i > high Then Exit Do
            Loop

            ' Find element on right that should be on left
            Do While scores(j) < pivot
                j = j - 1
                If j < low Then Exit Do
            Loop

            ' Swap elements and move indices
            If i <= j Then
                ' Swap scores
                tempScore = scores(i)
                scores(i) = scores(j)
                scores(j) = tempScore

                ' Swap rows
                tempRow = rows(i)
                rows(i) = rows(j)
                rows(j) = tempRow

                i = i + 1
                j = j - 1
            End If
        Loop Until i > j

        ' Push the larger partition to stack first (to minimize stack size)
        leftSize = j - low
        rightSize = high - i

        ' Push right partition if it exists
        If rightSize > 0 Then
            stackPos = stackPos + 1
            pivotStack(stackPos, 1) = i
            pivotStack(stackPos, 2) = high
        End If

        ' Push left partition if it exists
        If leftSize > 0 Then
            stackPos = stackPos + 1
            pivotStack(stackPos, 1) = low
            pivotStack(stackPos, 2) = j
        End If
    Loop
End Sub
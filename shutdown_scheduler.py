import os
import time
import psutil
import win32api
import win32con
import win32gui
import win32process
from datetime import datetime

# 当天几点几分关机
def shutdown_at_time(target_time):
    now = datetime.now()
    target = datetime.combine(now.date(), target_time)
    if target < now:
        target = datetime.combine(now.date() + timedelta(days=1), target_time)
    delay = (target - now).total_seconds()
    os.system(f'shutdown -s -t {int(delay)}')

# 等待多长时间后关机
def shutdown_after_delay(delay_seconds):
    os.system(f'shutdown -s -t {delay_seconds}')

# 开机多长时间后关机
def shutdown_after_boot(seconds_after_boot):
    boot_time = datetime.fromtimestamp(psutil.boot_time())
    now = datetime.now()
    elapsed = (now - boot_time).total_seconds()
    if elapsed < seconds_after_boot:
        remaining = seconds_after_boot - elapsed
        os.system(f'shutdown -s -t {int(remaining)}')

# 网络速度连续多长时间低于多少关机
def shutdown_on_low_network(threshold_kbps, duration_seconds):
    start_time = time.time()
    while True:
        net_io = psutil.net_io_counters()
        time.sleep(1)
        new_net_io = psutil.net_io_counters()
        speed = (new_net_io.bytes_recv - net_io.bytes_recv + new_net_io.bytes_sent - net_io.bytes_sent) * 8 / 1024
        if speed < threshold_kbps:
            if time.time() - start_time >= duration_seconds:
                os.system('shutdown -s -t 0')
                break
        else:
            start_time = time.time()

# 键盘、鼠标多长时间无操作关机
def shutdown_on_idle(idle_seconds):
    while True:
        idle_time = win32api.GetTickCount() - win32gui.GetLastInputInfo()
        if idle_time / 1000 >= idle_seconds:
            os.system('shutdown -s -t 0')
            break
        time.sleep(1)
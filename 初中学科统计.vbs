'------------------------------------------------------------------------------
' 模块：统计成绩
' 功能：统计各班级、各学校、全区的学科成绩
' 说明：从成绩总表中提取指定年级和科目的成绩数据，从课程表中获取任课教师信息，
'      并自动计算各班级、各学校和全区的统计数据，填充到学科统计表中。
'------------------------------------------------------------------------------

' 定义成绩统计结构体
' 用于存储各类统计数据
' 使用结构体可以提高代码可读性和维护性
Type 成绩统计类型
    实有人数 As Long
    参考人数 As Long
    及格人数 As Long
    优秀人数 As Long
    最高分 As Double
    最低分 As Double
    总分 As Double
    分数段人数(0 To 9) As Long
End Type

'------------------------------------------------------------------------------
' 主过程：统计学科成绩
'------------------------------------------------------------------------------
Sub 统计学科成绩()
    ' 初始化静态变量，确保每次运行都是从干净的状态开始
    Static 班级计数 As Long
    Static 学校计数 As Long
    班级计数 = 0
    学校计数 = 0

    ' 工作表变量
    Dim ws成绩总表 As Worksheet, ws学科统计 As Worksheet, ws课程表 As Worksheet

    ' 基本参数
    Dim 科目 As String, 年级 As String
    Dim 当前行 As Long
    Dim 科目列号 As Long, 课程表科目列号 As Long

    ' 数据存储
    Dim 成绩数据() As Variant  ' 用于存储成绩总表数据
    Dim 课程表数据() As Variant  ' 用于存储课程表数据
    Dim 学校班级字典 As Object  ' 存储学校和班级信息

    ' 排名相关变量
    Dim 班级得分数组() As Variant  ' 用于存储班级得分和行号，以便排序
    Dim 班级数量 As Long  ' 用于记录班级总数
    Dim 排名数组() As Long  ' 用于存储排名结果

    ' 学校排名相关变量
    Dim 学校得分数组() As Variant  ' 用于存储学校得分和行号，以便排序
    Dim 学校数量 As Long  ' 用于记录学校总数

    ' 循环变量
    Dim i As Long, j As Long, k As Long
    Dim 学校Key As Variant, 班级Key As Variant
    Dim 学校信息 As Variant

    ' 数据变量
    Dim 当前学校序号 As String, 当前学校 As String, 当前班级 As String
    Dim 当前学校序号值 As String, 当前学校值 As String
    Dim 学校键值 As String
    Dim 任课教师 As String

    ' 统计变量
    Dim 成绩 As Double
    Dim 实有人数 As Long, 参考人数 As Long
    Dim 及格人数 As Long, 优秀人数 As Long
    Dim 最高分 As Double, 最低分 As Double
    Dim 总分 As Double
    Dim 分数段人数(0 To 9) As Long

    ' 使用结构化错误处理
    On Error GoTo ErrorHandler

    ' 获取工作表
    Set ws成绩总表 = ThisWorkbook.Worksheets("成绩总表")
    Set ws学科统计 = ThisWorkbook.Worksheets("学科统计")
    Set ws课程表 = ThisWorkbook.Worksheets("课程表")

    ' 应用屏幕更新关闭，提高性能
    Application.ScreenUpdating = False

    ' 获取科目和年级
    科目 = ws学科统计.Range("D1").value
    年级 = ws学科统计.Range("I1").value

    ' 检查科目和年级是否有效
    If 科目 = "" Then
        MsgBox "请在D1单元格输入要统计的科目!", vbExclamation
        GoTo CleanExit
    End If

    If 年级 = "" Then
        MsgBox "请在I1单元格输入要统计的年级!", vbExclamation
        GoTo CleanExit
    End If

    ' 将数据加载到数组中，减少工作表访问，提高性能
    成绩数据 = ws成绩总表.UsedRange.value
    课程表数据 = ws课程表.UsedRange.value

    ' 查找科目在成绩总表中的列号
    科目列号 = 查找列号(成绩数据, 科目)

    ' 如果找不到科目，则退出
    If 科目列号 = 0 Then
        MsgBox "在成绩总表中找不到科目 """ & 科目 & """!", vbExclamation
        GoTo CleanExit
    End If

    ' 查找科目在课程表中的列号
    课程表科目列号 = 查找列号(课程表数据, 科目)

    ' 如果找不到科目，则提示但继续执行
    If 课程表科目列号 = 0 Then
        MsgBox "警告：在课程表中找不到科目 """ & 科目 & """，将无法获取任课教师信息。", vbExclamation
    End If

    ' 统计学校和班级数量
    Set 学校班级字典 = CreateObject("Scripting.Dictionary")

    ' 开始收集学校和班级信息

    ' 遍历成绩总表数据，收集所有符合年级条件的学校和班级
    For i = 2 To UBound(成绩数据, 1)
        ' 添加数组边界检查
        If i > UBound(成绩数据, 1) Then Exit For
        If UBound(成绩数据, 2) < 4 Then
            MsgBox "成绩总表数据格式不正确，列数不足！", vbCritical
            GoTo CleanExit
        End If

        ' 检查年级是否匹配
        If 成绩数据(i, 1) = 年级 Then
            ' 添加数据有效性检查
            If IsEmpty(成绩数据(i, 2)) Or IsEmpty(成绩数据(i, 3)) Or IsEmpty(成绩数据(i, 4)) Then
                ' 跳过无效数据行
                GoTo NextIteration
            End If

            当前学校序号 = CStr(成绩数据(i, 2))
            当前学校 = CStr(成绩数据(i, 3))
            当前班级 = CStr(成绩数据(i, 4))

            ' 将学校和班级添加到字典中
            学校键值 = 当前学校序号 & "|" & 当前学校

            If Not 学校班级字典.Exists(学校键值) Then
                学校班级字典.Add 学校键值, CreateObject("Scripting.Dictionary")
            End If

            If Not 学校班级字典(学校键值).Exists(当前班级) Then
                学校班级字典(学校键值).Add 当前班级, True
            End If
        End If
NextIteration:
    Next i

    ' 清空学科统计表（从第5行开始）
    If ws学科统计.UsedRange.Rows.Count > 4 Then
        ' 清空内容和格式
        With ws学科统计.Range("A5:Z" & ws学科统计.UsedRange.Rows.Count)
            .ClearContents   ' 清除内容
            .ClearFormats    ' 清除格式
            .Font.Bold = False  ' 重置粗体
            .Font.Color = RGB(0, 0, 0)  ' 重置字体颜色为黑色
            .Interior.ColorIndex = xlNone  ' 重置背景颜色
            .Borders.LineStyle = xlNone  ' 清除边框
        End With
    End If

    ' 添加排名列标题
    ws学科统计.Cells(4, 23).value = "全区排名"

    ' 当前行从第5行开始（标题在第4行）
    当前行 = 5

    ' 首先计算班级数量和学校数量，以便初始化数组
    班级数量 = 0
    学校数量 = 学校班级字典.Count

    ' 重置班级计数和学校计数，防止多次查询时累加
    班级计数 = 0
    学校计数 = 0

    For Each 学校Key In 学校班级字典.Keys
        For Each 班级Key In 学校班级字典(学校Key).Keys
            If 班级Key <> "全校" And 班级Key <> "全区" Then
                班级数量 = 班级数量 + 1
            End If
        Next 班级Key
    Next 学校Key

    ' 初始化班级得分数组
    ReDim 班级得分数组(1 To 班级数量, 1 To 3)  ' 列：1=平均分，2=行号，3=学校班级标识

    ' 初始化学校得分数组
    ReDim 学校得分数组(1 To 学校数量, 1 To 3)  ' 列：1=平均分，2=行号，3=学校名称

    ' 开始统计各学校和班级的成绩

    ' 遍历每个学校和班级进行统计
    For Each 学校Key In 学校班级字典.Keys
        学校信息 = Split(学校Key, "|")
        当前学校序号值 = 学校信息(0)
        当前学校值 = 学校信息(1)

        ' 遍历该学校的每个班级
        For Each 班级Key In 学校班级字典(学校Key).Keys
            当前班级 = 班级Key

            ' 创建并初始化统计数据结构体
            Dim 班级统计 As 成绩统计类型
            Call 初始化统计数据(班级统计)
            任课教师 = ""

            ' 遍历成绩总表，统计当前学校和班级的成绩
            For i = 2 To UBound(成绩数据, 1)
                ' 添加数组边界检查
                If i > UBound(成绩数据, 1) Then Exit For
                If UBound(成绩数据, 2) < 科目列号 Then
                    MsgBox "成绩总表数据格式不正确，科目列号超出范围！", vbCritical
                    GoTo CleanExit
                End If

                If 成绩数据(i, 1) = 年级 And _
                   成绩数据(i, 2) = 当前学校序号值 And _
                   成绩数据(i, 3) = 当前学校值 And _
                   成绩数据(i, 4) = 当前班级 Then

                    ' 实有人数加1
                    班级统计.实有人数 = 班级统计.实有人数 + 1

                    ' 获取成绩
                    If 科目列号 <= UBound(成绩数据, 2) Then
                        成绩 = Val(成绩数据(i, 科目列号))

                        ' 如果有成绩，则调用统计函数
                        If 成绩 > 0 Then
                            Call 统计单个成绩(成绩, 班级统计)
                        End If
                    End If

                    ' 任课教师信息已在外部循环中获取
                End If
            Next i

            ' 从课程表获取任课教师信息
            任课教师 = ""
            If 课程表科目列号 > 0 Then
                For k = 2 To UBound(课程表数据, 1)
                    ' 添加数组边界检查
                    If k > UBound(课程表数据, 1) Then Exit For
                    If UBound(课程表数据, 2) < 3 Then
                        MsgBox "课程表数据格式不正确，列数不足！", vbExclamation
                        GoTo CleanExit
                    End If
                    If UBound(课程表数据, 2) < 课程表科目列号 Then
                        MsgBox "课程表数据格式不正确，科目列号超出范围！", vbExclamation
                        GoTo CleanExit
                    End If

                    If 课程表数据(k, 1) = 当前学校值 And _
                       课程表数据(k, 2) = 当前班级 And _
                       课程表数据(k, 3) = 年级 Then
                        任课教师 = 课程表数据(k, 课程表科目列号)
                        Exit For
                    End If
                Next k
            End If

            ' 填充班级统计数据到学科统计表
            Call 填充统计数据(ws学科统计, 当前行, 班级统计, 当前学校序号值, 任课教师, 当前学校值, 当前班级)

            ' 记录班级得分率和行号，以便后续排名
            班级计数 = 班级计数 + 1

            ' 检查数组边界
            If 班级计数 > UBound(班级得分数组, 1) Then
                MsgBox "班级计数超出数组范围！请检查数据。", vbExclamation
                GoTo CleanExit
            End If

            ' 存储平均分、行号和学校班级标识
            If 班级统计.参考人数 > 0 Then
                班级得分数组(班级计数, 1) = 班级统计.总分 / 班级统计.参考人数  ' 平均分（用于排名）
            Else
                班级得分数组(班级计数, 1) = 0
            End If
            班级得分数组(班级计数, 2) = 当前行  ' 行号
            班级得分数组(班级计数, 3) = 当前学校值 & "-" & 当前班级  ' 学校班级标识

            ' 移动到下一行
            当前行 = 当前行 + 1
        Next 班级Key

        ' 添加学校汇总行
        ' 创建并初始化学校统计数据结构体
        Dim 学校统计 As 成绩统计类型
        Call 初始化统计数据(学校统计)

        ' 遍历成绩总表，统计当前学校的所有班级成绩
        For i = 2 To UBound(成绩数据, 1)
            ' 添加数组边界检查
            If i > UBound(成绩数据, 1) Then Exit For
            If UBound(成绩数据, 2) < 科目列号 Then
                MsgBox "成绩总表数据格式不正确，科目列号超出范围！", vbCritical
                GoTo CleanExit
            End If

            If 成绩数据(i, 1) = 年级 And _
               成绩数据(i, 2) = 当前学校序号值 And _
               成绩数据(i, 3) = 当前学校值 Then

                ' 实有人数加1
                学校统计.实有人数 = 学校统计.实有人数 + 1

                ' 获取成绩
                If 科目列号 <= UBound(成绩数据, 2) Then
                    成绩 = Val(成绩数据(i, 科目列号))

                    ' 如果有成绩，则调用统计函数
                    If 成绩 > 0 Then
                        Call 统计单个成绩(成绩, 学校统计)
                    End If
                End If
            End If
        Next i

        ' 填充学校汇总行
        Call 填充统计数据(ws学科统计, 当前行, 学校统计, 当前学校序号值, "", 当前学校值, "全校")

        ' 合并学校和班级单元格，显示为“XXXX学校汇总”
        With ws学科统计
            ' 合并学校和班级列
            .Range(.Cells(当前行, 3), .Cells(当前行, 4)).Merge
            ' 设置合并后的单元格值
            .Cells(当前行, 3).value = 当前学校值 & "汇总"
        End With

        ' 记录学校得分率和行号，以便后续排名
        学校计数 = 学校计数 + 1

        ' 检查数组边界
        If 学校计数 > UBound(学校得分数组, 1) Then
            MsgBox "学校计数超出数组范围！请检查数据。", vbExclamation
            GoTo CleanExit
        End If

        ' 存储平均分、行号和学校名称
        If 学校统计.参考人数 > 0 Then
            学校得分数组(学校计数, 1) = 学校统计.总分 / 学校统计.参考人数  ' 平均分（用于排名）
        Else
            学校得分数组(学校计数, 1) = 0
        End If
        学校得分数组(学校计数, 2) = 当前行  ' 行号
        学校得分数组(学校计数, 3) = 当前学校值  ' 学校名称

        ' 设置全校行的格式
        With ws学科统计.Range(ws学科统计.Cells(当前行, 1), ws学科统计.Cells(当前行, 23))
            .Font.Bold = True
            .Font.Color = RGB(255, 0, 0)  ' 全校行所有数据设置为红色字体
            .Interior.Color = RGB(255, 255, 0) ' 黄色背景
        End With

        ' 移动到下一行
        当前行 = 当前行 + 1
    Next 学校Key

    ' 添加全区汇总行
    ' 创建并初始化全区统计数据结构体
    Dim 全区统计 As 成绩统计类型
    Call 初始化统计数据(全区统计)

    ' 遍历成绩总表，统计全区成绩
    For i = 2 To UBound(成绩数据, 1)
        ' 添加数组边界检查
        If i > UBound(成绩数据, 1) Then Exit For
        If UBound(成绩数据, 2) < 科目列号 Then
            MsgBox "成绩总表数据格式不正确，科目列号超出范围！", vbCritical
            GoTo CleanExit
        End If

        If 成绩数据(i, 1) = 年级 Then
            ' 实有人数加1
            全区统计.实有人数 = 全区统计.实有人数 + 1

            ' 获取成绩
            If 科目列号 <= UBound(成绩数据, 2) Then
                成绩 = Val(成绩数据(i, 科目列号))

                ' 如果有成绩，则调用统计函数
                If 成绩 > 0 Then
                    Call 统计单个成绩(成绩, 全区统计)
                End If
            End If
        End If
    Next i

    ' 填充全区汇总行
    Call 填充统计数据(ws学科统计, 当前行, 全区统计, "", "", "全区", "全区")

    ' 合并学校和班级单元格，显示为“全区汇总”
    With ws学科统计
        ' 合并学校和班级列
        .Range(.Cells(当前行, 3), .Cells(当前行, 4)).Merge
        ' 设置合并后的单元格值
        .Cells(当前行, 3).value = "全区汇总"
    End With

    ' 设置全区行的格式
    With ws学科统计.Range(ws学科统计.Cells(当前行, 1), ws学科统计.Cells(当前行, 23))
        .Font.Bold = True
        .Font.Color = RGB(255, 0, 0)  ' 全区行所有数据设置为红色字体
        .Interior.Color = RGB(255, 255, 0) ' 黄色背景
    End With

    ' 计算并填充班级排名
    Call 计算班级排名(ws学科统计, 班级得分数组, 班级数量)

    ' 计算并填充学校排名
    Call 计算学校排名(ws学科统计, 学校得分数组, 学校数量)

    ' 格式化百分比列
    ws学科统计.Range("G5:G" & 当前行).NumberFormat = "0.00%"
    ws学科统计.Range("I5:I" & 当前行).NumberFormat = "0.00%"
    ws学科统计.Range("K5:K" & 当前行).NumberFormat = "0.00%"

    ' 添加边框
    Dim 数据区域 As Range
    Set 数据区域 = ws学科统计.Range(ws学科统计.Cells(5, 1), ws学科统计.Cells(当前行, 23))
    With 数据区域.Borders
        .LineStyle = xlContinuous
        .Weight = xlThin
        .ColorIndex = xlAutomatic
    End With

    ' 设置数据区域居中显示
    数据区域.HorizontalAlignment = xlCenter
    数据区域.VerticalAlignment = xlCenter

    ' 自动调整列宽
    ws学科统计.UsedRange.Columns.AutoFit

    ' 提示完成
    MsgBox "统计完成！共统计了 " & 当前行 - 5 & " 行数据。", vbInformation

    ' 清除错误处理
    On Error GoTo 0

    ' 正常退出点
CleanExit:
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    Exit Sub

    ' 错误处理
ErrorHandler:
    MsgBox "程序运行时发生错误：" & Err.Description, vbCritical
    Resume CleanExit
End Sub

'------------------------------------------------------------------------------
' 辅助函数：查找列号
' 功能：在数组的第一行中查找指定值的列号
' 参数：
'   - 数据数组：要搜索的二维数组
'   - 查找值：要查找的值
' 返回：如果找到返回列号，否则返回0
'------------------------------------------------------------------------------
Function 查找列号(数据数组 As Variant, 查找值 As String) As Long
    Dim i As Long
    查找列号 = 0

    ' 添加数组边界检查
    If Not IsArray(数据数组) Then Exit Function
    If UBound(数据数组, 1) < 1 Then Exit Function

    For i = 1 To UBound(数据数组, 2)
        If CStr(数据数组(1, i)) = 查找值 Then
            查找列号 = i
            Exit Function
        End If
    Next i
End Function

'------------------------------------------------------------------------------
' 辅助函数：获取数值
' 功能：将变量转换为数值，处理非数值情况
' 参数：
'   - value：要转换的值
' 返回：数值结果，如果非数值则返回0
'------------------------------------------------------------------------------
Function Val(value As Variant) As Double
    If IsNumeric(value) Then
        Val = CDbl(value)
    Else
        Val = 0
    End If
End Function

'------------------------------------------------------------------------------
' 辅助函数：统计成绩
' 功能：将成绩数据按照分数段进行统计
' 参数：
'   - 成绩：要统计的成绩值
'   - 统计数据：成绩统计类型结构体，用于存储统计结果
'------------------------------------------------------------------------------
Sub 统计单个成绩(成绩 As Double, ByRef 统计数据 As 成绩统计类型)
    ' 添加成绩有效性检查
    If 成绩 < 0 Or 成绩 > 100 Then Exit Sub

    ' 参考人数加一
    统计数据.参考人数 = 统计数据.参考人数 + 1
    统计数据.总分 = 统计数据.总分 + 成绩

    ' 更新最高分和最低分
    If 成绩 > 统计数据.最高分 Then 统计数据.最高分 = 成绩
    If 成绩 < 统计数据.最低分 Then 统计数据.最低分 = 成绩

    ' 统计及格和优秀人数
    If 成绩 >= 60 Then 统计数据.及格人数 = 统计数据.及格人数 + 1
    If 成绩 >= 80 Then 统计数据.优秀人数 = 统计数据.优秀人数 + 1

    ' 统计分数段
    If 成绩 = 100 Then
        统计数据.分数段人数(0) = 统计数据.分数段人数(0) + 1
    ElseIf 成绩 >= 90 Then
        统计数据.分数段人数(1) = 统计数据.分数段人数(1) + 1
    ElseIf 成绩 >= 80 Then
        统计数据.分数段人数(2) = 统计数据.分数段人数(2) + 1
    ElseIf 成绩 >= 70 Then
        统计数据.分数段人数(3) = 统计数据.分数段人数(3) + 1
    ElseIf 成绩 >= 60 Then
        统计数据.分数段人数(4) = 统计数据.分数段人数(4) + 1
    ElseIf 成绩 >= 50 Then
        统计数据.分数段人数(5) = 统计数据.分数段人数(5) + 1
    ElseIf 成绩 >= 40 Then
        统计数据.分数段人数(6) = 统计数据.分数段人数(6) + 1
    ElseIf 成绩 >= 30 Then
        统计数据.分数段人数(7) = 统计数据.分数段人数(7) + 1
    ElseIf 成绩 >= 20 Then
        统计数据.分数段人数(8) = 统计数据.分数段人数(8) + 1
    Else
        统计数据.分数段人数(9) = 统计数据.分数段人数(9) + 1
    End If
End Sub

'------------------------------------------------------------------------------
' 辅助函数：初始化统计数据
' 功能：初始化成绩统计类型结构体
' 参数：
'   - 统计数据：要初始化的成绩统计类型结构体
'------------------------------------------------------------------------------
Sub 初始化统计数据(ByRef 统计数据 As 成绩统计类型)
    Dim j As Long

    统计数据.实有人数 = 0
    统计数据.参考人数 = 0
    统计数据.及格人数 = 0
    统计数据.优秀人数 = 0
    统计数据.最高分 = 0
    统计数据.最低分 = 100
    统计数据.总分 = 0

    For j = 0 To 9
        统计数据.分数段人数(j) = 0
    Next j
End Sub

'------------------------------------------------------------------------------
' 辅助函数：填充统计数据
' 功能：将统计数据填充到学科统计表中
' 参数：
'   - ws学科统计：学科统计表
'   - 当前行：要填充的行号
'   - 统计数据：成绩统计类型结构体
'   - 学校序号：学校序号
'   - 任课教师：任课教师
'   - 学校名称：学校名称
'   - 班级名称：班级名称
'------------------------------------------------------------------------------
Sub 填充统计数据(ws学科统计 As Worksheet, 当前行 As Long, 统计数据 As 成绩统计类型, _
                  学校序号 As String, 任课教师 As String, 学校名称 As String, 班级名称 As String)
    Dim j As Long

    ' 如果最低分仍为初始值，说明没有有效成绩
    If 统计数据.最低分 = 100 And 统计数据.参考人数 = 0 Then 统计数据.最低分 = 0

    ' 填充学科统计表
    ws学科统计.Cells(当前行, 1).value = 学校序号  ' 学校序号
    ws学科统计.Cells(当前行, 2).value = 任课教师  ' 任课教师
    ws学科统计.Cells(当前行, 3).value = 学校名称  ' 学校
    ws学科统计.Cells(当前行, 4).value = 班级名称  ' 班级
    ws学科统计.Cells(当前行, 5).value = 统计数据.实有人数  ' 实有人数
    ws学科统计.Cells(当前行, 6).value = 统计数据.参考人数  ' 参考人数

    ' 计算平均得分率
    If 统计数据.参考人数 > 0 Then
        ws学科统计.Cells(当前行, 7).value = 统计数据.总分 / (统计数据.参考人数 * 100)  ' 平均得分率
    Else
        ws学科统计.Cells(当前行, 7).value = 0
    End If

    ' 计算平均分（用于排名）
    Dim 平均分 As Double
    If 统计数据.参考人数 > 0 Then
        平均分 = 统计数据.总分 / 统计数据.参考人数
    Else
        平均分 = 0
    End If

    ws学科统计.Cells(当前行, 8).value = 统计数据.及格人数  ' 及格人数

    ' 计算及格率
    If 统计数据.参考人数 > 0 Then
        ws学科统计.Cells(当前行, 9).value = 统计数据.及格人数 / 统计数据.参考人数  ' 及格率
    Else
        ws学科统计.Cells(当前行, 9).value = 0
    End If

    ws学科统计.Cells(当前行, 10).value = 统计数据.优秀人数  ' 优秀人数

    ' 计算优秀率
    If 统计数据.参考人数 > 0 Then
        ws学科统计.Cells(当前行, 11).value = 统计数据.优秀人数 / 统计数据.参考人数  ' 优秀率
    Else
        ws学科统计.Cells(当前行, 11).value = 0
    End If

    ws学科统计.Cells(当前行, 12).value = 统计数据.最高分  ' 最高分
    ws学科统计.Cells(当前行, 13).value = 统计数据.最低分  ' 最低分

    ' 填充分数段人数
    For j = 0 To 8
        ws学科统计.Cells(当前行, 14 + j).value = 统计数据.分数段人数(j)
    Next j
End Sub

'------------------------------------------------------------------------------
' 辅助函数：计算班级排名
' 功能：根据班级平均分计算排名并填充到学科统计表中
' 参数：
'   - ws学科统计：学科统计表
'   - 班级得分数组：存储班级平均分和行号的数组（平均分=总分/参考人数）
'   - 班级数量：班级总数
'------------------------------------------------------------------------------
Sub 计算班级排名(ws学科统计 As Worksheet, 班级得分数组 As Variant, 班级数量 As Long)
    Dim i As Long, j As Long
    Dim 临时得分 As Double, 临时行号 As Long, 临时标识 As String
    Dim 排名数组() As Long

    ' 如果班级数量小于1，则无需排名
    If 班级数量 < 1 Then Exit Sub

    ' 初始化排名数组
    ReDim 排名数组(1 To 班级数量)

    ' 对班级得分数组按平均分降序排序（冒泡排序）
    ' 添加数组边界检查
    If UBound(班级得分数组, 2) < 3 Then
        MsgBox "班级得分数组格式不正确，列数不足！", vbExclamation
        Exit Sub
    End If

    For i = 1 To 班级数量 - 1
        ' 添加数组边界检查
        If i > UBound(班级得分数组, 1) Then Exit For

        For j = i + 1 To 班级数量
            ' 添加数组边界检查
            If j > UBound(班级得分数组, 1) Then Exit For

            If 班级得分数组(i, 1) < 班级得分数组(j, 1) Then
                ' 交换平均分
                临时得分 = 班级得分数组(i, 1)
                班级得分数组(i, 1) = 班级得分数组(j, 1)
                班级得分数组(j, 1) = 临时得分

                ' 交换行号
                临时行号 = 班级得分数组(i, 2)
                班级得分数组(i, 2) = 班级得分数组(j, 2)
                班级得分数组(j, 2) = 临时行号

                ' 交换标识
                临时标识 = 班级得分数组(i, 3)
                班级得分数组(i, 3) = 班级得分数组(j, 3)
                班级得分数组(j, 3) = 临时标识
            End If
        Next j
    Next i

    ' 计算排名（处理并列情况）
    Dim 当前排名 As Long, 当前得分 As Double
    当前排名 = 0
    当前得分 = -1  ' 初始化为不可能的得分值

    For i = 1 To 班级数量
        ' 添加数组边界检查
        If i > UBound(班级得分数组, 1) Then Exit For

        ' 如果当前得分与前一个不同，排名为当前位置
        If 班级得分数组(i, 1) <> 当前得分 Then
            当前排名 = i
            当前得分 = 班级得分数组(i, 1)
        End If

        ' 记录排名
        If i <= UBound(排名数组) Then
            排名数组(i) = 当前排名
        End If
    Next i

    ' 填充排名到学科统计表
    For i = 1 To 班级数量
        ' 添加数组边界检查
        If i > UBound(班级得分数组, 1) Then Exit For
        If i > UBound(排名数组) Then Exit For

        ' 获取行号
        Dim 行号 As Long
        行号 = 班级得分数组(i, 2)

        ' 填充排名
        ws学科统计.Cells(行号, 23).value = 排名数组(i)
    Next i
End Sub

'------------------------------------------------------------------------------
' 辅助函数：计算学校排名
' 功能：根据学校平均分计算排名并填充到学科统计表中
' 参数：
'   - ws学科统计：学科统计表
'   - 学校得分数组：存储学校平均分和行号的数组（平均分=总分/参考人数）
'   - 学校数量：学校总数
'------------------------------------------------------------------------------
Sub 计算学校排名(ws学科统计 As Worksheet, 学校得分数组 As Variant, 学校数量 As Long)
    Dim i As Long, j As Long
    Dim 临时得分 As Double, 临时行号 As Long, 临时名称 As String
    Dim 排名数组() As Long

    ' 如果学校数量小于1，则无需排名
    If 学校数量 < 1 Then Exit Sub

    ' 初始化排名数组
    ReDim 排名数组(1 To 学校数量)

    ' 对学校得分数组按平均分降序排序（冒泡排序）
    ' 添加数组边界检查
    If UBound(学校得分数组, 2) < 3 Then
        MsgBox "学校得分数组格式不正确，列数不足！", vbExclamation
        Exit Sub
    End If

    For i = 1 To 学校数量 - 1
        ' 添加数组边界检查
        If i > UBound(学校得分数组, 1) Then Exit For

        For j = i + 1 To 学校数量
            ' 添加数组边界检查
            If j > UBound(学校得分数组, 1) Then Exit For

            If 学校得分数组(i, 1) < 学校得分数组(j, 1) Then
                ' 交换平均分
                临时得分 = 学校得分数组(i, 1)
                学校得分数组(i, 1) = 学校得分数组(j, 1)
                学校得分数组(j, 1) = 临时得分

                ' 交换行号
                临时行号 = 学校得分数组(i, 2)
                学校得分数组(i, 2) = 学校得分数组(j, 2)
                学校得分数组(j, 2) = 临时行号

                ' 交换学校名称
                临时名称 = 学校得分数组(i, 3)
                学校得分数组(i, 3) = 学校得分数组(j, 3)
                学校得分数组(j, 3) = 临时名称
            End If
        Next j
    Next i

    ' 计算排名（处理并列情况）
    Dim 当前排名 As Long, 当前得分 As Double
    当前排名 = 0
    当前得分 = -1  ' 初始化为不可能的得分值

    For i = 1 To 学校数量
        ' 添加数组边界检查
        If i > UBound(学校得分数组, 1) Then Exit For

        ' 如果当前得分与前一个不同，排名为当前位置
        If 学校得分数组(i, 1) <> 当前得分 Then
            当前排名 = i
            当前得分 = 学校得分数组(i, 1)
        End If

        ' 记录排名
        If i <= UBound(排名数组) Then
            排名数组(i) = 当前排名
        End If
    Next i

    ' 填充排名到学科统计表
    For i = 1 To 学校数量
        ' 添加数组边界检查
        If i > UBound(学校得分数组, 1) Then Exit For
        If i > UBound(排名数组) Then Exit For

        ' 获取行号
        Dim 行号 As Long
        行号 = 学校得分数组(i, 2)

        ' 填充排名（红色粗体）
        ws学科统计.Cells(行号, 23).value = 排名数组(i)
        ws学科统计.Cells(行号, 23).Font.Bold = True
        ws学科统计.Cells(行号, 23).Font.Color = RGB(255, 0, 0)  ' 红色
    Next i
End Sub
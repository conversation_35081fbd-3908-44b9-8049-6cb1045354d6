Sub 排名统计()

    Dim QP(1 To 180, 1 To 25)
    Dim arr, crr, drr(), p, s, 总大小, 间隔, x, y, i, j, k, l As Integer
    Dim temp1, temp2 As Single
    ReDim drr(1 To 14, 1 To 25)

    crr = Array("思茅一中", "普洱二中", "思茅三中", "思茅四中", "思茅六中", "逸夫中学", "云仙中学", "龙潭中学", "思茅港中", "六顺中学", "育英学校", "博雅公学", "普洱市民中", "普洱市一中")
    Application.DisplayAlerts = False
    o = Sheets("成绩总表").Range("C65536").End(xlUp).Row

    ' 获取成绩总表数据
    arr = Sheets("成绩总表").Range("A2:T" & o).value
    Sheets("排名分布").Activate
    Range("C5:V18").ClearContents

    Application.ScreenUpdating = False    '关闭系统提示

    ' 筛选并计算符合年级条件的数据数量
    s = 0
    For i = 1 To UBound(arr)
        If arr(i, 1) = Sheets("排名分布").Cells(1, 3).value Then
            s = s + 1
        End If
    Next

    If s = 0 Then
        MsgBox "数据不存在,请查看成绩总表！"
        Sheets("排名分布").Activate
        Exit Sub
    End If

    ' 创建临时数组存储符合条件的数据
    ReDim tempArr(1 To s, 1 To 2)

    ' 将符合年级条件的数据复制到临时数组中
    s = 1
    For i = 1 To UBound(arr)
        If arr(i, 1) = Sheets("排名分布").Cells(1, 3).value Then
            tempArr(s, 1) = arr(i, 3)   ' 学校列
            tempArr(s, 2) = arr(i, 18) ' 获取对应年级的学科:成绩
            s = s + 1
        End If
    Next

    总大小 = s - 1
    间隔 = 1
    If 总大小 > 100 Then
        Do While 间隔 < 总大小
            间隔 = 间隔 * 3 + 1
        Loop
        间隔 = 间隔 \ 9
    End If

    ' 使用希尔排序按成绩降序排列
    Do While 间隔
        For x = 1 + 间隔 To 总大小
            temp1 = tempArr(x, 1)  ' 学校
            temp2 = tempArr(x, 2) ' 成绩
            For y = x - 间隔 To 1 Step -间隔
                If tempArr(y, 2) >= temp2 Then Exit For
                tempArr(y + 间隔, 1) = tempArr(y, 1)
                tempArr(y + 间隔, 2) = tempArr(y, 2)
            Next y
            tempArr(y + 间隔, 1) = temp1
            tempArr(y + 间隔, 2) = temp2
        Next x
        间隔 = 间隔 \ 3
    Loop

    Sheets("排名分布").Activate

    ' 统计各学校在不同排名区间的人数
    For j = 1 To 24
        x = Mid(Cells(4, j + 2), 2, 4)
        For k = 1 To x  ' 前N名
            l = Application.Match(tempArr(k, 1), crr, 0)
            drr(l, j) = drr(l, j) + 1
        Next k
        Sheets("排名分布").Range("C5").Resize(14, 24) = drr
    Next j

    Erase drr
    Erase tempArr

End Sub

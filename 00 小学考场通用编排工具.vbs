' ========================================
' 考场编排通用工具 - 重构版
' 功能：实现WPS中的考场编排，支持桌号管理
' 数据结构：学校、校点、年级、班级、准考号、姓名、考场号、桌号
' 桌号概念：单人座位（每桌1人，桌号不重复），双人座位（每桌2人，桌号可重复）
' 编排规则：各学校各校点独立建考场序号，交叉编排避免同班相邻
' 作者：AI Assistant
' 日期：2024
' 版本：v3.0
' ========================================

Option Explicit

' 全局变量
Dim g_ExamRoomCapacity As Integer      ' 考场容量（桌子数量）
Dim g_SeatingMode As Integer           ' 座位模式：1=单人，2=双人
Dim g_GradeGroups(1 To 4) As String   ' 年级组合（最多4种）
Dim g_WorksheetName As String          ' 工作表名称

' 数据结构常量
Const COL_SCHOOL = 1      ' 学校列
Const COL_CAMPUS = 2      ' 校点列
Const COL_GRADE = 3       ' 年级列
Const COL_CLASS = 4       ' 班级列
Const COL_EXAMNO = 5      ' 准考号列
Const COL_NAME = 6        ' 姓名列
Const COL_ROOMNO = 7      ' 考场号列
Const COL_TABLENO = 8     ' 桌号列

' 主程序入口
Sub Main()
    On Error GoTo ErrorHandler

    ' 初始化
    g_WorksheetName = "考场编排"

    ' 检查工作表是否存在
    If Not WorksheetExists(g_WorksheetName) Then
        MsgBox "未找到'" & g_WorksheetName & "'工作表，请确认工作表名称正确！", vbCritical, "错误"
        Exit Sub
    End If

    ' 显示设置对话框
    If ShowSettingsDialog() Then
        ' 开始考场编排
        Call ArrangeExamRooms
        MsgBox "考场编排完成！", vbInformation, "完成"
    End If
    Exit Sub

ErrorHandler:
    MsgBox "程序执行出错：" & Err.Description, vbCritical, "错误"
End Sub

' 检查工作表是否存在
Function WorksheetExists(wsName As String) As Boolean
    Dim ws As Worksheet
    WorksheetExists = False

    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = wsName Then
            WorksheetExists = True
            Exit For
        End If
    Next ws
End Function

' 显示设置对话框
Function ShowSettingsDialog() As Boolean
    Dim result As VbMsgBoxResult
    Dim capacity As String
    Dim mode As String
    Dim gradeInput As String
    Dim i As Integer

    ShowSettingsDialog = False

    ' 获取考场容量（桌子数量）
    capacity = InputBox("请输入考场容量（每个考场的桌子数量）：", "考场容量设置", "30")
    If capacity = "" Then Exit Function

    If Not IsNumeric(capacity) Or CInt(capacity) <= 0 Then
        MsgBox "请输入有效的桌子数量！", vbExclamation, "输入错误"
        Exit Function
    End If
    g_ExamRoomCapacity = CInt(capacity)

    ' 获取座位模式
    result = MsgBox("请选择座位模式：" & vbCrLf & vbCrLf & _
                   "是(Y) = 双人座位（每桌坐2人，桌号可重复）" & vbCrLf & _
                   "否(N) = 单人座位（每桌坐1人，桌号不重复）", vbYesNo + vbQuestion, "座位模式选择")

    If result = vbYes Then
        g_SeatingMode = 2  ' 双人

        ' 双人模式下设置年级组合
        For i = 1 To 4
            gradeInput = InputBox("请输入第" & i & "个年级组合（例如：三,四年级）：" & vbCrLf & _
                                "留空表示不设置该组合", "年级组合设置 " & i & "/4", "")
            g_GradeGroups(i) = gradeInput

            If gradeInput = "" And i = 1 Then
                MsgBox "至少需要设置一个年级组合！", vbExclamation, "设置错误"
                Exit Function
            ElseIf gradeInput = "" Then
                Exit For
            End If
        Next i
    Else
        g_SeatingMode = 1  ' 单人
        ' 单人模式下不需要年级组合
    End If

    ShowSettingsDialog = True
End Function

' 考场编排主程序
Sub ArrangeExamRooms()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim dataArray As Variant
    Dim i As Long

    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    On Error GoTo ErrorHandler

    ' 获取工作表
    Set ws = ThisWorkbook.Worksheets(g_WorksheetName)

    ' 获取数据范围
    lastRow = ws.Cells(ws.Rows.Count, COL_SCHOOL).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "工作表中没有数据！", vbExclamation, "数据错误"
        GoTo CleanUp
    End If

    ' 验证数据结构
    If Not ValidateDataStructure(ws) Then
        MsgBox "数据结构不正确！请确保列结构为：学校、校点、年级、班级、准考号、姓名、考场号、桌号", vbCritical, "数据错误"
        GoTo CleanUp
    End If

    ' 读取数据到数组（提高性能）
    dataArray = ws.Range("A1:H" & lastRow).Value

    ' 清空考场号和桌号列
    ws.Range("G2:H" & lastRow).ClearContents

    ' 根据座位模式进行编排
    If g_SeatingMode = 1 Then
        ' 单人座位编排
        Call ArrangeSingleSeating(ws, dataArray, lastRow)
    Else
        ' 双人座位编排
        Call ArrangeDoubleSeating(ws, dataArray, lastRow)
    End If

CleanUp:
    ' 恢复设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub

ErrorHandler:
    MsgBox "编排过程中发生错误：" & Err.Description, vbCritical, "错误"
    GoTo CleanUp
End Sub

' 验证数据结构
Function ValidateDataStructure(ws As Worksheet) As Boolean
    ValidateDataStructure = False

    ' 检查必要的列是否存在数据
    If ws.Cells(1, COL_SCHOOL).Value = "" Or _
       ws.Cells(1, COL_CAMPUS).Value = "" Or _
       ws.Cells(1, COL_GRADE).Value = "" Or _
       ws.Cells(1, COL_CLASS).Value = "" Then
        Exit Function
    End If

    ValidateDataStructure = True
End Function

' 单人座位编排（每桌1人，桌号不重复）
Sub ArrangeSingleSeating(ws As Worksheet, dataArray As Variant, lastRow As Long)
    Dim campusDict As Object
    Dim campusKey As String
    Dim currentSchool As String, currentCampus As String
    Dim roomNumber As Integer, tableNumber As Integer
    Dim i As Long

    ' 创建字典用于按学校+校点分组
    Set campusDict = CreateObject("Scripting.Dictionary")

    ' 按学校+校点分组数据
    For i = 2 To lastRow
        If dataArray(i, COL_SCHOOL) <> "" And dataArray(i, COL_NAME) <> "" Then
            currentSchool = CStr(dataArray(i, COL_SCHOOL))
            currentCampus = CStr(dataArray(i, COL_CAMPUS))
            campusKey = currentSchool & "|" & currentCampus

            If Not campusDict.Exists(campusKey) Then
                campusDict.Add campusKey, CreateObject("Scripting.Dictionary")
            End If

            ' 添加学生信息
            Dim studentInfo As String
            studentInfo = i & "|" & CStr(dataArray(i, COL_GRADE)) & "|" & CStr(dataArray(i, COL_CLASS)) & "|" & _
                         CStr(dataArray(i, COL_EXAMNO)) & "|" & CStr(dataArray(i, COL_NAME))

            Dim studentCount As Integer
            studentCount = campusDict(campusKey).Count + 1
            campusDict(campusKey).Add studentCount, studentInfo
        End If
    Next i

    ' 为每个学校+校点独立编排考场
    Dim campusKeys As Variant
    campusKeys = campusDict.keys

    Dim j As Integer, k As Integer
    For j = 0 To UBound(campusKeys)
        campusKey = campusKeys(j)

        ' 每个学校+校点重置考场号和桌号
        roomNumber = 1
        tableNumber = 1

        ' 获取当前校点的学生
        Dim students As Object
        Set students = campusDict(campusKey)

        ' 创建交叉编排的学生序列
        Dim arrangedStudents As Variant
        arrangedStudents = CreateCrossArrangement(students)

        ' 为交叉编排后的学生分配考场和桌号
        For k = 1 To UBound(arrangedStudents)
            If arrangedStudents(k) <> "" Then
                Dim studentData As Variant
                studentData = Split(arrangedStudents(k), "|")
                Dim rowIndex As Integer
                rowIndex = CInt(studentData(0))

                ' 写入考场号和桌号
                ws.Cells(rowIndex, COL_ROOMNO).Value = roomNumber
                ws.Cells(rowIndex, COL_TABLENO).Value = tableNumber

                ' 更新桌号（单人座位：每桌1人，桌号不重复）
                tableNumber = tableNumber + 1

                ' 检查是否需要换考场
                If tableNumber > g_ExamRoomCapacity Then
                    roomNumber = roomNumber + 1
                    tableNumber = 1
                End If
            End If
        Next k
    Next j
End Sub

' 双人座位编排（每桌2人，桌号可重复）
Sub ArrangeDoubleSeating(ws As Worksheet, dataArray As Variant, lastRow As Long)
    Dim campusDict As Object
    Dim campusKey As String
    Dim currentSchool As String, currentCampus As String
    Dim roomNumber As Integer, tableNumber As Integer
    Dim i As Long

    ' 创建字典用于按学校+校点分组
    Set campusDict = CreateObject("Scripting.Dictionary")

    ' 按学校+校点分组数据
    For i = 2 To lastRow
        If dataArray(i, COL_SCHOOL) <> "" And dataArray(i, COL_NAME) <> "" Then
            currentSchool = CStr(dataArray(i, COL_SCHOOL))
            currentCampus = CStr(dataArray(i, COL_CAMPUS))
            campusKey = currentSchool & "|" & currentCampus

            If Not campusDict.Exists(campusKey) Then
                campusDict.Add campusKey, CreateObject("Scripting.Dictionary")
            End If

            ' 添加学生信息
            Dim studentInfo As String
            studentInfo = i & "|" & CStr(dataArray(i, COL_GRADE)) & "|" & CStr(dataArray(i, COL_CLASS)) & "|" & _
                         CStr(dataArray(i, COL_EXAMNO)) & "|" & CStr(dataArray(i, COL_NAME))

            Dim studentCount As Integer
            studentCount = campusDict(campusKey).Count + 1
            campusDict(campusKey).Add studentCount, studentInfo
        End If
    Next i

    ' 为每个学校+校点独立编排考场
    Dim campusKeys As Variant
    campusKeys = campusDict.keys

    Dim j As Integer, k As Integer
    For j = 0 To UBound(campusKeys)
        campusKey = campusKeys(j)

        ' 每个学校+校点重置考场号和桌号
        roomNumber = 1
        tableNumber = 1

        ' 获取当前校点的学生
        Dim students As Object
        Set students = campusDict(campusKey)

        ' 按年级组合进行分组和配对
        Dim pairedStudents As Variant
        pairedStudents = CreateGradePairedArrangement(students)

        ' 为配对后的学生分配考场和桌号
        Dim pairCount As Integer
        pairCount = UBound(pairedStudents, 1)

        For k = 1 To pairCount
            ' 处理A座学生
            If pairedStudents(k, 1) <> "" Then
                Dim studentDataA As Variant
                studentDataA = Split(pairedStudents(k, 1), "|")
                Dim rowIndexA As Integer
                rowIndexA = CInt(studentDataA(0))

                ws.Cells(rowIndexA, COL_ROOMNO).Value = roomNumber
                ws.Cells(rowIndexA, COL_TABLENO).Value = tableNumber
            End If

            ' 处理B座学生
            If pairedStudents(k, 2) <> "" Then
                Dim studentDataB As Variant
                studentDataB = Split(pairedStudents(k, 2), "|")
                Dim rowIndexB As Integer
                rowIndexB = CInt(studentDataB(0))

                ws.Cells(rowIndexB, COL_ROOMNO).Value = roomNumber
                ws.Cells(rowIndexB, COL_TABLENO).Value = tableNumber
            End If

            ' 更新桌号（双人座位：每桌2人，桌号可重复）
            tableNumber = tableNumber + 1

            ' 检查是否需要换考场
            If tableNumber > g_ExamRoomCapacity Then
                roomNumber = roomNumber + 1
                tableNumber = 1
            End If
        Next k
    Next j
End Sub

' 获取年级所属的组合
Function GetGradeGroup(grade As String) As String
    Dim i As Integer
    GetGradeGroup = ""

    For i = 1 To 4
        If g_GradeGroups(i) <> "" Then
            If InStr(g_GradeGroups(i), grade) > 0 Then
                GetGradeGroup = "组" & i
                Exit For
            End If
        End If
    Next i
End Function

' 创建交叉编排的学生序列（避免同班学生相邻）
Function CreateCrossArrangement(students As Object) As Variant
    Dim studentCount As Integer
    Dim classDict As Object
    Dim i As Integer
    Dim currentClass As String
    Dim studentData As Variant

    studentCount = students.Count

    ' 边界检查
    If studentCount = 0 Then
        Dim emptyArray(1 To 1) As String
        emptyArray(1) = ""
        CreateCrossArrangement = emptyArray
        Exit Function
    End If

    ' 按班级分组学生
    Set classDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        If UBound(studentData) >= 2 Then
            currentClass = studentData(2)  ' 班级信息

            If Not classDict.Exists(currentClass) Then
                classDict.Add currentClass, CreateObject("Scripting.Dictionary")
            End If

            Dim classStudentCount As Integer
            classStudentCount = classDict(currentClass).Count + 1
            classDict(currentClass).Add classStudentCount, students(i)
        End If
    Next i

    ' 创建交叉编排数组
    Dim arrangedArray() As String
    ReDim arrangedArray(1 To studentCount)

    Dim classKeys As Variant
    classKeys = classDict.keys
    Dim classCount As Integer
    classCount = UBound(classKeys) + 1

    ' 如果只有一个班级，直接返回原序列
    If classCount = 1 Then
        For i = 1 To studentCount
            arrangedArray(i) = students(i)
        Next i
        CreateCrossArrangement = arrangedArray
        Exit Function
    End If

    ' 多个班级时进行交叉编排
    Dim arrangedIndex As Integer
    arrangedIndex = 1

    ' 找出最大班级人数
    Dim maxStudentsPerClass As Integer
    maxStudentsPerClass = 0

    For i = 0 To UBound(classKeys)
        If classDict(classKeys(i)).Count > maxStudentsPerClass Then
            maxStudentsPerClass = classDict(classKeys(i)).Count
        End If
    Next i

    ' 按轮次从各班级取学生进行交叉编排
    Dim round As Integer
    For round = 1 To maxStudentsPerClass
        For i = 0 To UBound(classKeys)
            currentClass = classKeys(i)
            If round <= classDict(currentClass).Count And arrangedIndex <= studentCount Then
                arrangedArray(arrangedIndex) = classDict(currentClass)(round)
                arrangedIndex = arrangedIndex + 1
            End If
        Next i
    Next round

    CreateCrossArrangement = arrangedArray
End Function

' 创建年级配对编排（双人座位模式）
Function CreateGradePairedArrangement(students As Object) As Variant
    Dim studentCount As Integer
    Dim gradeDict As Object
    Dim i As Integer
    Dim currentGrade As String
    Dim studentData As Variant

    studentCount = students.Count

    ' 边界检查
    If studentCount = 0 Then
        Dim emptyArray(1 To 1, 1 To 2) As String
        emptyArray(1, 1) = ""
        emptyArray(1, 2) = ""
        CreateGradePairedArrangement = emptyArray
        Exit Function
    End If

    ' 按年级分组学生
    Set gradeDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        If UBound(studentData) >= 1 Then
            currentGrade = studentData(1)  ' 年级信息

            If Not gradeDict.Exists(currentGrade) Then
                gradeDict.Add currentGrade, CreateObject("Scripting.Dictionary")
            End If

            Dim gradeStudentCount As Integer
            gradeStudentCount = gradeDict(currentGrade).Count + 1
            gradeDict(currentGrade).Add gradeStudentCount, students(i)
        End If
    Next i

    ' 计算配对数量
    Dim pairCount As Integer
    pairCount = (studentCount + 1) \ 2

    Dim pairedArray As Variant
    ReDim pairedArray(1 To pairCount, 1 To 2)

    Dim gradeKeys As Variant
    gradeKeys = gradeDict.keys
    Dim gradeCount As Integer
    gradeCount = UBound(gradeKeys) + 1

    ' 根据年级组合设置进行配对
    If gradeCount = 1 Then
        ' 单年级：按班级交叉编排后配对
        Dim singleGradeStudents As Object
        Set singleGradeStudents = gradeDict(gradeKeys(0))

        Dim arrangedStudents As Variant
        arrangedStudents = CreateCrossArrangement(singleGradeStudents)

        ' 顺序配对
        For i = 1 To pairCount
            If (i - 1) * 2 + 1 <= UBound(arrangedStudents) Then
                pairedArray(i, 1) = arrangedStudents((i - 1) * 2 + 1)
            Else
                pairedArray(i, 1) = ""
            End If

            If (i - 1) * 2 + 2 <= UBound(arrangedStudents) Then
                pairedArray(i, 2) = arrangedStudents((i - 1) * 2 + 2)
            Else
                pairedArray(i, 2) = ""
            End If
        Next i
    Else
        ' 多年级：跨年级配对（确保同桌来自不同年级）
        Dim pairIndex As Integer
        pairIndex = 1

        ' 找出最大年级人数
        Dim maxStudentsPerGrade As Integer
        maxStudentsPerGrade = 0

        For i = 0 To UBound(gradeKeys)
            If gradeDict(gradeKeys(i)).Count > maxStudentsPerGrade Then
                maxStudentsPerGrade = gradeDict(gradeKeys(i)).Count
            End If
        Next i

        ' 按轮次从各年级取学生进行配对
        Dim round As Integer
        For round = 1 To maxStudentsPerGrade
            Dim gradeIndex As Integer
            For gradeIndex = 0 To UBound(gradeKeys) - 1 Step 2
                If pairIndex <= pairCount Then
                    ' A座：第一个年级的学生
                    If round <= gradeDict(gradeKeys(gradeIndex)).Count Then
                        pairedArray(pairIndex, 1) = gradeDict(gradeKeys(gradeIndex))(round)
                    Else
                        pairedArray(pairIndex, 1) = ""
                    End If

                    ' B座：第二个年级的学生
                    If gradeIndex + 1 <= UBound(gradeKeys) And round <= gradeDict(gradeKeys(gradeIndex + 1)).Count Then
                        pairedArray(pairIndex, 2) = gradeDict(gradeKeys(gradeIndex + 1))(round)
                    Else
                        pairedArray(pairIndex, 2) = ""
                    End If

                    pairIndex = pairIndex + 1
                End If
            Next gradeIndex
        Next round
    End If

    CreateGradePairedArrangement = pairedArray
End Function

' 为年级组创建交叉编排的学生序列（双人座位模式）
Function CreateCrossClassArrangementForGroup(students As Object) As Variant
    Dim studentCount As Integer
    Dim classDict As Object
    Dim i As Integer
    Dim currentClass As String
    Dim studentData As Variant

    studentCount = students.Count

    ' 按班级分组学生
    Set classDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        currentClass = studentData(2)  ' 班级信息

        If Not classDict.Exists(currentClass) Then
            classDict.Add currentClass, CreateObject("Scripting.Dictionary")
        End If

        Dim classStudentCount As Integer
        classStudentCount = classDict(currentClass).Count + 1
        classDict(currentClass).Add classStudentCount, students(i)
    Next i

    ' 创建交叉编排数组
    Dim arrangedArray() As String
    ReDim arrangedArray(1 To studentCount)

    Dim classKeys As Variant
    classKeys = classDict.keys
    Dim classCount As Integer
    classCount = UBound(classKeys) + 1

    ' 如果只有一个班级，直接返回原序列
    If classCount = 1 Then
        For i = 1 To studentCount
            arrangedArray(i) = students(i)
        Next i
        CreateCrossClassArrangementForGroup = arrangedArray
        Exit Function
    End If

    ' 多个班级时进行交叉编排
    Dim arrangedIndex As Integer
    arrangedIndex = 1

    ' 找出最大班级人数
    Dim maxStudentsPerClass As Integer
    maxStudentsPerClass = 0

    For i = 0 To UBound(classKeys)
        If classDict(classKeys(i)).Count > maxStudentsPerClass Then
            maxStudentsPerClass = classDict(classKeys(i)).Count
        End If
    Next i

    ' 按轮次从各班级取学生进行交叉编排
    Dim round As Integer
    For round = 1 To maxStudentsPerClass
        For i = 0 To UBound(classKeys)
            currentClass = classKeys(i)
            If round <= classDict(currentClass).Count Then
                arrangedArray(arrangedIndex) = classDict(currentClass)(round)
                arrangedIndex = arrangedIndex + 1
            End If
        Next i
    Next round

    CreateCrossClassArrangementForGroup = arrangedArray
End Function

' 从交叉编排后的数组创建配对数组
Function CreatePairedArrayFromArranged(arrangedStudents As Variant) As Variant
    Dim studentCount As Integer
    Dim pairedArray As Variant
    Dim i As Integer

    studentCount = UBound(arrangedStudents)

    ' 如果学生数为奇数，需要多一个位置
    Dim pairCount As Integer
    pairCount = (studentCount + 1) \ 2

    ReDim pairedArray(1 To pairCount, 1 To 2)

    ' 将交叉编排后的学生配对
    For i = 1 To pairCount
        If (i - 1) * 2 + 1 <= studentCount Then
            pairedArray(i, 1) = arrangedStudents((i - 1) * 2 + 1)
        Else
            pairedArray(i, 1) = ""
        End If

        If (i - 1) * 2 + 2 <= studentCount Then
            pairedArray(i, 2) = arrangedStudents((i - 1) * 2 + 2)
        Else
            pairedArray(i, 2) = ""
        End If
    Next i

    CreatePairedArrayFromArranged = pairedArray
End Function

' 重新编号字典
Sub ReindexDictionary(dict As Object)
    Dim keys As Variant
    Dim values As Variant
    Dim i As Integer

    If dict.Count = 0 Then Exit Sub

    keys = dict.keys
    values = dict.Items

    dict.RemoveAll

    For i = 0 To UBound(keys)
        dict.Add i + 1, values(i)
    Next i
End Sub
Function CreateCrossClassArrangementForSingleGrade1(students As Object) As Variant
    Dim studentCount As Integer
    Dim classDict As Object
    Dim i As Integer
    Dim currentClass As String
    Dim studentData As Variant


    studentCount = students.Count

    ' 边界检查
    If studentCount = 0 Then
        Dim emptyArray(1 To 1) As String
        emptyArray(1) = ""
        CreateCrossClassArrangementForSingleGrade1 = emptyArray
        Exit Function
    End If

    ' 按班级分组学生
    Set classDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        If UBound(studentData) >= 2 Then  ' 确保数据完整
            currentClass = studentData(2)  ' 班级信息

            If Not classDict.Exists(currentClass) Then
                classDict.Add currentClass, CreateObject("Scripting.Dictionary")
            End If

            Dim classStudentCount As Integer
            classStudentCount = classDict(currentClass).Count + 1
            classDict(currentClass).Add classStudentCount, students(i)
        End If
    Next i

    ' 创建交叉编排数组
    Dim arrangedArray() As String
    ReDim arrangedArray(1 To studentCount)

    Dim classKeys As Variant
    classKeys = classDict.keys
    Dim classCount As Integer
    classCount = UBound(classKeys) + 1

    ' 如果只有一个班级，直接返回原序列
    If classCount = 1 Then
        For i = 1 To studentCount
            arrangedArray(i) = students(i)
        Next i
        CreateCrossClassArrangementForSingleGrade1 = arrangedArray
        Exit Function
    End If

    ' 多个班级时进行交叉编排
    Dim arrangedIndex As Integer
    arrangedIndex = 1

    ' 找出最大班级人数
    Dim maxStudentsPerClass As Integer
    maxStudentsPerClass = 0

    For i = 0 To UBound(classKeys)
        If classDict(classKeys(i)).Count > maxStudentsPerClass Then
            maxStudentsPerClass = classDict(classKeys(i)).Count
        End If
    Next i

    ' 按轮次从各班级取学生进行交叉编排
    Dim round As Integer
    For round = 1 To maxStudentsPerClass
        For i = 0 To UBound(classKeys)
            currentClass = classKeys(i)
            If round <= classDict(currentClass).Count And arrangedIndex <= studentCount Then
                arrangedArray(arrangedIndex) = classDict(currentClass)(round)
                arrangedIndex = arrangedIndex + 1
            End If
        Next i
    Next round

    CreateCrossClassArrangementForSingleGrade1 = arrangedArray
End Function

' 为单个年级创建交叉班级编排
Function CreateCrossClassArrangementForSingleGrade(students As Object) As Variant
    Dim studentCount As Integer
    Dim classDict As Object
    Dim i As Integer
    Dim currentClass As String
    Dim studentData As Variant

    studentCount = students.Count

    ' 边界检查
    If studentCount = 0 Then
        Dim emptyArray(1 To 1) As String
        emptyArray(1) = ""
        CreateCrossClassArrangementForSingleGrade = emptyArray
        Exit Function
    End If

    ' 按班级分组学生
    Set classDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        If UBound(studentData) >= 2 Then  ' 确保数据完整
            currentClass = studentData(2)  ' 班级信息

            If Not classDict.Exists(currentClass) Then
                classDict.Add currentClass, CreateObject("Scripting.Dictionary")
            End If

            Dim classStudentCount As Integer
            classStudentCount = classDict(currentClass).Count + 1
            classDict(currentClass).Add classStudentCount, students(i)
        End If
    Next i

    ' 创建交叉编排数组
    Dim arrangedArray() As String
    ReDim arrangedArray(1 To studentCount)

    Dim classKeys As Variant
    classKeys = classDict.keys
    Dim classCount As Integer
    classCount = UBound(classKeys) + 1

    ' 如果只有一个班级，直接返回原序列
    If classCount = 1 Then
        For i = 1 To studentCount
            arrangedArray(i) = students(i)
        Next i
        CreateCrossClassArrangementForSingleGrade = arrangedArray
        Exit Function
    End If

    ' 多个班级时进行交叉编排
    Dim arrangedIndex As Integer
    arrangedIndex = 1

    ' 找出最大班级人数
    Dim maxStudentsPerClass As Integer
    maxStudentsPerClass = 0

    For i = 0 To UBound(classKeys)
        If classDict(classKeys(i)).Count > maxStudentsPerClass Then
            maxStudentsPerClass = classDict(classKeys(i)).Count
        End If
    Next i

    ' 按轮次从各班级取学生进行交叉编排
    Dim round As Integer
    For round = 1 To maxStudentsPerClass
        For i = 0 To UBound(classKeys)
            currentClass = classKeys(i)
            If round <= classDict(currentClass).Count And arrangedIndex <= studentCount Then
                arrangedArray(arrangedIndex) = classDict(currentClass)(round)
                arrangedIndex = arrangedIndex + 1
            End If
        Next i
    Next round

    CreateCrossClassArrangementForSingleGrade = arrangedArray
End Function

' 创建跨年级配对数组（确保同桌来自不同年级）
Function CreateCrossGradePairing(students As Object) As Variant
    Dim studentCount As Integer
    Dim gradeDict As Object
    Dim i As Integer
    Dim currentGrade As String
    Dim studentData As Variant

    studentCount = students.Count

    ' 边界检查
    If studentCount = 0 Then
        Dim emptyArray(1 To 1, 1 To 2) As String
        emptyArray(1, 1) = ""
        emptyArray(1, 2) = ""
        CreateCrossGradePairing = emptyArray
        Exit Function
    End If

    ' 按年级分组学生
    Set gradeDict = CreateObject("Scripting.Dictionary")

    For i = 1 To studentCount
        studentData = Split(students(i), "|")
        If UBound(studentData) >= 1 Then  ' 确保数据完整
            currentGrade = studentData(1)  ' 年级信息

            If Not gradeDict.Exists(currentGrade) Then
                gradeDict.Add currentGrade, CreateObject("Scripting.Dictionary")
            End If

            Dim gradeStudentCount As Integer
            gradeStudentCount = gradeDict(currentGrade).Count + 1
            gradeDict(currentGrade).Add gradeStudentCount, students(i)
        End If
    Next i

    ' 检查是否有有效的年级数据
    If gradeDict.Count = 0 Then
        Dim emptyArray2(1 To 1, 1 To 2) As String
        emptyArray2(1, 1) = ""
        emptyArray2(1, 2) = ""
        CreateCrossGradePairing = emptyArray2
        Exit Function
    End If

    ' 计算配对数量
    Dim pairCount As Integer
    pairCount = (studentCount + 1) \ 2

    Dim pairedArray As Variant
    ReDim pairedArray(1 To pairCount, 1 To 2)

    Dim gradeKeys As Variant
    gradeKeys = gradeDict.keys
    Dim gradeCount As Integer
    gradeCount = UBound(gradeKeys) + 1

    ' 如果只有一个年级，按班级进行交叉编排后配对
    If gradeCount = 1 Then
        Dim singleGradeStudents As Object
        Set singleGradeStudents = gradeDict(gradeKeys(0))

        ' 按班级交叉编排
        Dim arrangedStudents As Variant
        arrangedStudents = CreateCrossClassArrangementForSingleGrade1(singleGradeStudents)

        ' 顺序配对
        For i = 1 To pairCount
            If (i - 1) * 2 + 1 <= UBound(arrangedStudents) Then
                pairedArray(i, 1) = arrangedStudents((i - 1) * 2 + 1)
            Else
                pairedArray(i, 1) = ""
            End If

            If (i - 1) * 2 + 2 <= UBound(arrangedStudents) Then
                pairedArray(i, 2) = arrangedStudents((i - 1) * 2 + 2)
            Else
                pairedArray(i, 2) = ""
            End If
        Next i
    Else
        ' 多个年级时进行跨年级配对
        Dim pairIndex As Integer
        pairIndex = 1

        ' 轮流从不同年级取学生进行配对
        Dim maxStudentsPerGrade As Integer
        maxStudentsPerGrade = 0

        ' 找出最大年级人数
        For i = 0 To UBound(gradeKeys)
            If gradeDict(gradeKeys(i)).Count > maxStudentsPerGrade Then
                maxStudentsPerGrade = gradeDict(gradeKeys(i)).Count
            End If
        Next i

        ' 按轮次从各年级取学生进行配对
        Dim round As Integer
        For round = 1 To maxStudentsPerGrade
            Dim gradeIndex As Integer
            For gradeIndex = 0 To UBound(gradeKeys) - 1 Step 2  ' 每次取两个年级
                If pairIndex <= pairCount Then
                    ' A座：第一个年级的学生
                    If round <= gradeDict(gradeKeys(gradeIndex)).Count Then
                        pairedArray(pairIndex, 1) = gradeDict(gradeKeys(gradeIndex))(round)
                    Else
                        pairedArray(pairIndex, 1) = ""
                    End If

                    ' B座：第二个年级的学生
                    If gradeIndex + 1 <= UBound(gradeKeys) And round <= gradeDict(gradeKeys(gradeIndex + 1)).Count Then
                        pairedArray(pairIndex, 2) = gradeDict(gradeKeys(gradeIndex + 1))(round)
                    Else
                        pairedArray(pairIndex, 2) = ""
                    End If

                    pairIndex = pairIndex + 1
                End If
            Next gradeIndex
        Next round
    End If

    CreateCrossGradePairing = pairedArray
End Function

' 辅助函数：显示进度信息（可选）
Sub ShowProgress(message As String)
    ' 可以在状态栏显示进度信息
    Application.StatusBar = message
End Sub